import { Arc } from './shapes/Arc';
import { Arrow } from './shapes/Arrow';
import { Circle } from './shapes/Circle';
import { Ellipse } from './shapes/Ellipse';
import { Image } from './shapes/Image';
import { Label, Tag } from './shapes/Label';
import { Line } from './shapes/Line';
import { Path } from './shapes/Path';
import { Rect } from './shapes/Rect';
import { RegularPolygon } from './shapes/RegularPolygon';
import { Ring } from './shapes/Ring';
import { Sprite } from './shapes/Sprite';
import { Star } from './shapes/Star';
import { Text } from './shapes/Text';
import { TextPath } from './shapes/TextPath';
import { Transformer } from './shapes/Transformer';
import { Wedge } from './shapes/Wedge';
export declare const Konva: {
    _global: any;
    version: string;
    isBrowser: boolean;
    isUnminified: boolean;
    dblClickWindow: number;
    getAngle(angle: number): number;
    enableTrace: boolean;
    pointerEventsEnabled: boolean;
    autoDrawEnabled: boolean;
    hitOnDragEnabled: boolean;
    capturePointerEventsEnabled: boolean;
    _mouseListenClick: boolean;
    _touchListenClick: boolean;
    _pointerListenClick: boolean;
    _mouseInDblClickWindow: boolean;
    _touchInDblClickWindow: boolean;
    _pointerInDblClickWindow: boolean;
    _mouseDblClickPointerId: any;
    _touchDblClickPointerId: any;
    _pointerDblClickPointerId: any;
    pixelRatio: number;
    dragDistance: number;
    angleDeg: boolean;
    showWarnings: boolean;
    dragButtons: number[];
    isDragging(): any;
    isDragReady(): boolean;
    releaseCanvasOnDestroy: boolean;
    document: any;
    _injectGlobal(Konva: any): void;
} & {
    Util: {
        _isElement(obj: any): obj is Element;
        _isFunction(obj: any): boolean;
        _isPlainObject(obj: any): boolean;
        _isArray(obj: any): obj is any[];
        _isNumber(obj: any): obj is number;
        _isString(obj: any): obj is string;
        _isBoolean(obj: any): obj is boolean;
        isObject(val: any): val is Object;
        isValidSelector(selector: any): boolean;
        _sign(number: number): 1 | -1;
        requestAnimFrame(callback: Function): void;
        createCanvasElement(): HTMLCanvasElement;
        createImageElement(): HTMLImageElement;
        _isInDocument(el: any): boolean;
        _urlToImage(url: string, callback: Function): void;
        _rgbToHex(r: number, g: number, b: number): string;
        _hexToRgb(hex: string): import("./types").RGB;
        getRandomColor(): string;
        getRGB(color: string): import("./types").RGB;
        colorToRGBA(str: string): import("./types").RGBA;
        _namedColorToRBA(str: string): {
            r: any;
            g: any;
            b: any;
            a: number;
        };
        _rgbColorToRGBA(str: string): import("./types").RGBA;
        _rgbaColorToRGBA(str: string): import("./types").RGBA;
        _hex8ColorToRGBA(str: string): import("./types").RGBA;
        _hex6ColorToRGBA(str: string): import("./types").RGBA;
        _hex4ColorToRGBA(str: string): import("./types").RGBA;
        _hex3ColorToRGBA(str: string): import("./types").RGBA;
        _hslColorToRGBA(str: string): import("./types").RGBA;
        haveIntersection(r1: import("./types").IRect, r2: import("./types").IRect): boolean;
        cloneObject<Any>(obj: Any): Any;
        cloneArray(arr: any[]): any[];
        degToRad(deg: number): number;
        radToDeg(rad: number): number;
        _degToRad(deg: number): number;
        _radToDeg(rad: number): number;
        _getRotation(radians: any): any;
        _capitalize(str: string): string;
        throw(str: string): never;
        error(str: string): void;
        warn(str: string): void;
        each(obj: any, func: any): void;
        _inRange(val: any, left: any, right: any): boolean;
        _getProjectionToSegment(x1: any, y1: any, x2: any, y2: any, x3: any, y3: any): any[];
        _getProjectionToLine(pt: import("./types").Vector2d, line: any, isClosed: any): import("./types").Vector2d;
        _prepareArrayForTween(startArray: any, endArray: any, isClosed: any): any[];
        _prepareToStringify(obj: any): any;
        _assign<T, U>(target: T, source: U): T & U;
        _getFirstPointerId(evt: any): any;
        releaseCanvas(...canvases: HTMLCanvasElement[]): void;
        drawRoundedRectPath(context: import("./Context").Context, width: number, height: number, cornerRadius: number | number[]): void;
    };
    Transform: typeof import("./Util").Transform;
    Node: typeof import("./Node").Node;
    Container: typeof import("./Container").Container;
    Stage: typeof import("./Stage").Stage;
    stages: import("./Stage").Stage[];
    Layer: typeof import("./Layer").Layer;
    FastLayer: typeof import("./FastLayer").FastLayer;
    Group: typeof import("./Group").Group;
    DD: {
        readonly isDragging: boolean;
        justDragged: boolean;
        readonly node: import("./Node").Node<import("./Node").NodeConfig>;
        _dragElements: Map<number, {
            node: import("./Node").Node<import("./Node").NodeConfig>;
            startPointerPos: import("./types").Vector2d;
            offset: import("./types").Vector2d;
            pointerId?: number;
            dragStatus: "ready" | "dragging" | "stopped";
        }>;
        _drag(evt: any): void;
        _endDragBefore(evt?: any): void;
        _endDragAfter(evt: any): void;
    };
    Shape: typeof import("./Shape").Shape;
    shapes: {
        [key: string]: import("./Shape").Shape<import("./Shape").ShapeConfig>;
    };
    Animation: typeof import("./Animation").Animation;
    Tween: typeof import("./Tween").Tween;
    Easings: {
        BackEaseIn(t: any, b: any, c: any, d: any): any;
        BackEaseOut(t: any, b: any, c: any, d: any): any;
        BackEaseInOut(t: any, b: any, c: any, d: any): any;
        ElasticEaseIn(t: any, b: any, c: any, d: any, a: any, p: any): any;
        ElasticEaseOut(t: any, b: any, c: any, d: any, a: any, p: any): any;
        ElasticEaseInOut(t: any, b: any, c: any, d: any, a: any, p: any): any;
        BounceEaseOut(t: any, b: any, c: any, d: any): any;
        BounceEaseIn(t: any, b: any, c: any, d: any): any;
        BounceEaseInOut(t: any, b: any, c: any, d: any): any;
        EaseIn(t: any, b: any, c: any, d: any): any;
        EaseOut(t: any, b: any, c: any, d: any): any;
        EaseInOut(t: any, b: any, c: any, d: any): any;
        StrongEaseIn(t: any, b: any, c: any, d: any): any;
        StrongEaseOut(t: any, b: any, c: any, d: any): any;
        StrongEaseInOut(t: any, b: any, c: any, d: any): any;
        Linear(t: any, b: any, c: any, d: any): any;
    };
    Context: typeof import("./Context").Context;
    Canvas: typeof import("./Canvas").Canvas;
} & {
    Arc: typeof Arc;
    Arrow: typeof Arrow;
    Circle: typeof Circle;
    Ellipse: typeof Ellipse;
    Image: typeof Image;
    Label: typeof Label;
    Tag: typeof Tag;
    Line: typeof Line;
    Path: typeof Path;
    Rect: typeof Rect;
    RegularPolygon: typeof RegularPolygon;
    Ring: typeof Ring;
    Sprite: typeof Sprite;
    Star: typeof Star;
    Text: typeof Text;
    TextPath: typeof TextPath;
    Transformer: typeof Transformer;
    Wedge: typeof Wedge;
    Filters: {
        Blur: import("./Node").Filter;
        Brighten: import("./Node").Filter;
        Contrast: import("./Node").Filter;
        Emboss: import("./Node").Filter;
        Enhance: import("./Node").Filter;
        Grayscale: import("./Node").Filter;
        HSL: import("./Node").Filter;
        HSV: import("./Node").Filter;
        Invert: import("./Node").Filter;
        Kaleidoscope: import("./Node").Filter;
        Mask: import("./Node").Filter;
        Noise: import("./Node").Filter;
        Pixelate: import("./Node").Filter;
        Posterize: import("./Node").Filter;
        RGB: import("./Node").Filter;
        RGBA: import("./Node").Filter;
        Sepia: import("./Node").Filter;
        Solarize: import("./Node").Filter;
        Threshold: import("./Node").Filter;
    };
};
