import type { WellKnownSymbols } from './constants';
import type { SerovalAggregateErrorNode, SerovalArrayBufferNode, SerovalArrayNode, SerovalAsyncIteratorFactoryInstanceNode, SerovalBigIntNode, SerovalBigIntTypedArrayNode, SerovalBoxedNode, SerovalConstantNode, SerovalDataViewNode, SerovalDateNode, SerovalErrorNode, SerovalIndexedValueNode, SerovalIteratorFactoryInstanceNode, SerovalNode, SerovalNodeWithID, SerovalNumberNode, SerovalObjectRecordNode, SerovalPluginNode, SerovalReferenceNode, SerovalRegExpNode, SerovalSetNode, SerovalStreamConstructorNode, SerovalStreamNextNode, SerovalStreamReturnNode, SerovalStreamThrowNode, SerovalStringNode, SerovalTypedArrayNode, SerovalWKSymbolNode } from './types';
import type { BigIntTypedArrayValue, TypedArrayValue } from './utils/typed-array';
export declare function createNumberNode(value: number): SerovalConstantNode | SerovalNumberNode;
export declare function createStringNode(value: string): SerovalStringNode;
export declare function createBigIntNode(current: bigint): SerovalBigIntNode;
export declare function createIndexedValueNode(id: number): SerovalIndexedValueNode;
export declare function createDateNode(id: number, current: Date): SerovalDateNode;
export declare function createRegExpNode(id: number, current: RegExp): SerovalRegExpNode;
export declare function createArrayBufferNode(id: number, current: ArrayBuffer): SerovalArrayBufferNode;
export declare function createWKSymbolNode(id: number, current: WellKnownSymbols): SerovalWKSymbolNode;
export declare function createReferenceNode<T>(id: number, ref: T): SerovalReferenceNode;
export declare function createPluginNode(id: number, tag: string, value: unknown): SerovalPluginNode;
export declare function createArrayNode(id: number, current: unknown[], parsedItems: SerovalNode[]): SerovalArrayNode;
export declare function createBoxedNode(id: number, boxed: SerovalNode): SerovalBoxedNode;
export declare function createTypedArrayNode(id: number, current: TypedArrayValue, buffer: SerovalNode): SerovalTypedArrayNode;
export declare function createBigIntTypedArrayNode(id: number, current: BigIntTypedArrayValue, buffer: SerovalNode): SerovalBigIntTypedArrayNode;
export declare function createDataViewNode(id: number, current: DataView, buffer: SerovalNode): SerovalDataViewNode;
export declare function createErrorNode(id: number, current: Error, options: SerovalObjectRecordNode | undefined): SerovalErrorNode;
export declare function createAggregateErrorNode(id: number, current: AggregateError, options: SerovalObjectRecordNode | undefined): SerovalAggregateErrorNode;
export declare function createSetNode(id: number, size: number, items: SerovalNode[]): SerovalSetNode;
export declare function createIteratorFactoryInstanceNode(factory: SerovalNodeWithID, items: SerovalNode): SerovalIteratorFactoryInstanceNode;
export declare function createAsyncIteratorFactoryInstanceNode(factory: SerovalNodeWithID, items: SerovalNode): SerovalAsyncIteratorFactoryInstanceNode;
export declare function createStreamConstructorNode(id: number, factory: SerovalNodeWithID, sequence: SerovalNode[]): SerovalStreamConstructorNode;
export declare function createStreamNextNode(id: number, parsed: SerovalNode): SerovalStreamNextNode;
export declare function createStreamThrowNode(id: number, parsed: SerovalNode): SerovalStreamThrowNode;
export declare function createStreamReturnNode(id: number, parsed: SerovalNode): SerovalStreamReturnNode;
//# sourceMappingURL=base-primitives.d.ts.map