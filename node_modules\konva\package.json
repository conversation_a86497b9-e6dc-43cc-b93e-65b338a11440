{"name": "konva", "version": "8.4.3", "author": "<PERSON>", "files": ["README.md", "konva.js", "konva.min.js", "lib", "cmj"], "main": "./cmj/index-node.js", "browser": "./lib/index.js", "typings": "./lib/index-types.d.ts", "type": "module", "__ignore_exports": {".": {"node": {"require": "./cmj/index-node.js", "import": "./lib/index-node.js", "default": "./lib/index-node.js"}, "require": "./cmj/index.js", "import": "./lib/index.js", "default": "./lib/index.js"}, "./cmj": {"import": "./cmj/index.js", "require": "./cmj/index-node.js", "node": "./cmj/index-node.js", "default": "./cmj/index.js"}, "./cmj/*": {"import": "./cmj/*.js", "require": "./cmj/*.js", "default": "./cmj/*.js"}, "./lib/*": {"import": "./cmj/*.js", "require": "./cmj/*.js", "default": "./cmj/*.js"}}, "scripts": {"start": "npm run test:watch", "compile": "npm run clean && npm run tsc && cp ./src/index-types.d.ts ./lib/index-types.d.ts && npm run rollup && cp ./package-cmj.json ./cmj/package.json && cp ./src/index-types.d.ts ./cmj/index-types.d.ts", "build": "npm run compile && cp ./src/index-types.d.ts ./lib && gulp build && node ./rename-imports.mjs", "test:import": "npm run build  && node ./test/import-test.cjs &&node ./test/import-test.mjs", "test": "npm run test:browser && npm run test:node", "test:build": "parcel build ./test/unit-tests.html --dist-dir ./test-build --target none --public-url ./ --no-source-maps", "test:browser": "npm run test:build && mocha-headless-chrome -f ./test-build/unit-tests.html -a disable-web-security", "test:node": "ts-mocha -p ./test/tsconfig.json test/unit/**/*.ts --exit && npm run test:import", "test:watch": "rm -rf ./parcel-cache && parcel serve ./test/unit-tests.html ./test/manual-tests.html ./test/sandbox.html", "tsc": "tsc --removeComments && tsc --build ./tsconfig-cmj.json", "rollup": "rollup -c --bundleConfigAsCjs", "clean": "rm -rf ./lib && rm -rf ./types && rm -rf ./cmj && rm -rf ./test-build", "watch": "rollup -c -w", "size": "size-limit"}, "targets": {"none": {}}, "funding": [{"type": "patreon", "url": "https://www.patreon.com/lavrton"}, {"type": "opencollective", "url": "https://opencollective.com/konva"}, {"type": "github", "url": "https://github.com/sponsors/lavrton"}], "size-limit": [{"limit": "45 KB", "path": "./lib/index.js"}, {"limit": "26 KB", "path": "./lib/Core.js"}, {"path": "./konva.min.js"}], "devDependencies": {"@parcel/transformer-image": "2.8.2", "@size-limit/preset-big-lib": "^8.1.0", "@types/mocha": "^10.0.1", "canvas": "^2.10.2", "chai": "4.3.7", "filehound": "^1.17.6", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-connect": "^5.7.0", "gulp-exec": "^5.0.0", "gulp-jsdoc3": "^3.0.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.3", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "gulp-uglify-es": "^3.0.0", "gulp-util": "^3.0.8", "mocha": "10.2.0", "mocha-headless-chrome": "^4.0.0", "parcel": "2.8.2", "process": "^0.11.10", "rollup": "^3.7.5", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-typescript2": "^0.34.1", "size-limit": "^8.1.0", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.4"}, "keywords": ["canvas", "animations", "graphic", "html5"], "prettier": {"singleQuote": true}, "bugs": {"url": "https://github.com/konvajs/konva/issues"}, "homepage": "http://konvajs.org/", "readmeFilename": "README.md", "repository": {"type": "git", "url": "git://github.com/konvajs/konva.git"}, "license": "MIT"}