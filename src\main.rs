mod models;
mod handlers;
mod services;
mod utils;
mod resp;
mod error;
mod cache;
mod middleware;
mod config;

use salvo::prelude::*;
use std::path::PathBuf;
use cache::paper_cache::PaperCache;
use std::sync::OnceLock;
use tokio::signal;

// 全局缓存实例
static PAPER_CACHE: OnceLock<PaperCache> = OnceLock::new();

/// 获取全局缓存实例
pub fn get_cache() -> &'static PaperCache {
    PAPER_CACHE.get().expect("Cache not initialized")
}

#[tokio::main]
async fn main() {
    // 初始化配置
    if let Err(e) = config::init_config() {
        eprintln!("配置初始化失败: {}", e);
        std::process::exit(1);
    }

    let config = config::get_config();

    // 确保papers目录存在
    let papers_dir = PathBuf::from(&config.storage.papers_directory);
    if !papers_dir.exists() {
        tokio::fs::create_dir_all(&papers_dir).await.expect("Failed to create papers directory");
    }

    // 初始化全局缓存
    let cache = PaperCache::new();
    if let Err(e) = cache.initialize().await {
        eprintln!("缓存初始化失败: {}", e);
        std::process::exit(1);
    }

    // 设置全局缓存实例
    PAPER_CACHE.set(cache).expect("Failed to set global cache");

    // 打印缓存统计信息
    let stats = get_cache().get_stats().await;
    println!("缓存统计: {} 篇文献, {} 个文件夹", stats.papers_count, stats.folders_count);

    let router = Router::new()
        .hoop(middleware::performance::performance_middleware)
        .push(
            Router::with_path("/health")
                .get(handlers::paper::health_check)
        )
        .push(
            Router::with_path("/api")
                .push(
                    Router::with_path("/folders")
                        .get(handlers::folder::list_folders)
                        .post(handlers::folder::create_folder)
                        .push(
                            Router::with_path("/{**path}")
                                .delete(handlers::folder::delete_folder)
                                .put(handlers::folder::rename_folder)
                        )
                        .push(
                            Router::with_path("/force/{**path}")
                                .delete(handlers::folder::delete_folder_force)
                        )
                )
                .push(
                    Router::with_path("/papers")
                        .get(handlers::paper::list_papers)
                        .post(handlers::paper::create_paper)
                        .push(
                            Router::with_path("/migrate")
                                .post(handlers::paper::migrate_all_papers)
                        )
                        .push(
                            Router::with_path("/cache/stats")
                                .get(handlers::paper::cache_stats)
                        )
                        .push(
                            Router::with_path("/cache/reload")
                                .post(handlers::paper::cache_reload)
                        )

                        .push(
                            Router::with_path("/stats/detailed")
                                .get(handlers::paper::get_detailed_stats)
                        )
                        .push(
                            Router::with_path("/folders/tree")
                                .get(handlers::paper::get_folder_tree)
                        )
                        .push(
                            Router::with_path("/{id}")
                                .get(handlers::paper::get_paper)
                                .put(handlers::paper::update_paper)
                                .delete(handlers::paper::delete_paper)
                                .push(
                                    Router::with_path("/files")
                                        .get(handlers::paper::list_files)
                                        .post(handlers::paper::upload_file)
                                        .push(
                                            Router::with_path("/{filename}")
                                                .get(handlers::paper::download_file)
                                                .delete(handlers::paper::delete_file)
                                                .put(handlers::paper::rename_file)
                                        )
                                        .push(
                                            Router::with_path("/preview/{filename}")
                                                .get(handlers::paper::preview_file)
                                        )
                                )
                        )
                )
        );

    println!("正在启动服务器...");
    let server_address = config.get_server_address();
    let acceptor = TcpListener::new(&server_address).bind().await;
    println!("服务器已启动，监听地址: http://{}", server_address);
    println!("应用名称: {}", config.server.name);
    println!("应用描述: {}", config.server.description);

    // 设置信号处理
    tokio::spawn(async {
        #[cfg(unix)]
        {
            let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())
                .expect("failed to install signal handler");
            let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())
                .expect("failed to install signal handler");

            tokio::select! {
                _ = sigterm.recv() => println!("\n收到 SIGTERM 信号"),
                _ = sigint.recv() => println!("\n收到 SIGINT 信号"),
            }
        }

        #[cfg(windows)]
        {
            signal::ctrl_c().await.expect("failed to listen for ctrl-c");
            println!("\n收到 Ctrl+C 信号");
        }

        println!("正在优雅关闭服务器...");
        std::process::exit(0);
    });

    // 启动服务器
    Server::new(acceptor).serve(router).await;
}
