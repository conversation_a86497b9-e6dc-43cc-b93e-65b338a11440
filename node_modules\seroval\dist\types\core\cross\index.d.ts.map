{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/cross/index.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAC5C,OAAO,KAAK,EAAE,8BAA8B,EAAE,MAAM,SAAS,CAAC;AAE9D,OAAO,KAAK,EAAE,+BAA+B,EAAE,MAAM,gBAAgB,CAAC;AAEtE,OAAO,KAAK,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,MAAM,UAAU,CAAC;AAE/E,OAAO,KAAK,EAAE,+BAA+B,EAAE,MAAM,UAAU,CAAC;AAEhE,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,QAAQ,CAAC;AAG5D,MAAM,WAAW,qBACf,SAAQ,6BAA6B,EACnC,mBAAmB;CAAG;AAE1B,wBAAgB,cAAc,CAAC,CAAC,EAC9B,MAAM,EAAE,CAAC,EACT,OAAO,GAAE,qBAA0B,GAClC,MAAM,CAeR;AAED,MAAM,WAAW,0BACf,SAAQ,8BAA8B,EACpC,mBAAmB;CAAG;AAE1B,wBAAsB,mBAAmB,CAAC,CAAC,EACzC,MAAM,EAAE,CAAC,EACT,OAAO,GAAE,0BAA+B,GACvC,OAAO,CAAC,MAAM,CAAC,CAejB;AAED,MAAM,MAAM,kBAAkB,GAAG,yBAAyB,CAAC;AAE3D,wBAAgB,WAAW,CAAC,CAAC,EAC3B,MAAM,EAAE,CAAC,EACT,OAAO,GAAE,yBAA8B,GACtC,WAAW,CAQb;AAED,MAAM,MAAM,uBAAuB,GAAG,yBAAyB,CAAC;AAEhE,wBAAsB,gBAAgB,CAAC,CAAC,EACtC,MAAM,EAAE,CAAC,EACT,OAAO,GAAE,yBAA8B,GACtC,OAAO,CAAC,WAAW,CAAC,CAQtB;AAED,MAAM,WAAW,2BACf,SAAQ,IAAI,CAAC,+BAA+B,EAAE,SAAS,CAAC,EACtD,mBAAmB;IACrB,WAAW,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;CACvD;AAED,wBAAgB,oBAAoB,CAAC,CAAC,EACpC,MAAM,EAAE,CAAC,EACT,OAAO,EAAE,2BAA2B,GACnC,MAAM,IAAI,CAkCZ;AAED,MAAM,MAAM,wBAAwB,GAAG,+BAA+B,CAAC;AAEvE,wBAAgB,iBAAiB,CAAC,CAAC,EACjC,MAAM,EAAE,CAAC,EACT,OAAO,EAAE,wBAAwB,GAChC,MAAM,IAAI,CAcZ;AAED,MAAM,MAAM,oBAAoB,GAAG,+BAA+B,CAAC;AAEnE,wBAAgB,aAAa,CAAC,CAAC,EAC7B,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,oBAAoB,GAC5B,CAAC,CAOH"}