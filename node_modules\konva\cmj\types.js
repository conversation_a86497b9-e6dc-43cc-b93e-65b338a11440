"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KonvaNodeEvent = void 0;
var KonvaNodeEvent;
(function (KonvaNodeEvent) {
    KonvaNodeEvent["mouseover"] = "mouseover";
    KonvaNodeEvent["mouseout"] = "mouseout";
    KonvaNodeEvent["mousemove"] = "mousemove";
    KonvaNodeEvent["mouseleave"] = "mouseleave";
    KonvaNodeEvent["mouseenter"] = "mouseenter";
    KonvaNodeEvent["mousedown"] = "mousedown";
    KonvaNodeEvent["mouseup"] = "mouseup";
    KonvaNodeEvent["wheel"] = "wheel";
    KonvaNodeEvent["contextmenu"] = "contextmenu";
    KonvaNodeEvent["click"] = "click";
    KonvaNodeEvent["dblclick"] = "dblclick";
    KonvaNodeEvent["touchstart"] = "touchstart";
    KonvaNodeEvent["touchmove"] = "touchmove";
    KonvaNodeEvent["touchend"] = "touchend";
    KonvaNodeEvent["tap"] = "tap";
    KonvaNodeEvent["dbltap"] = "dbltap";
    KonvaNodeEvent["dragstart"] = "dragstart";
    KonvaNodeEvent["dragmove"] = "dragmove";
    KonvaNodeEvent["dragend"] = "dragend";
})(KonvaNodeEvent = exports.KonvaNodeEvent || (exports.KonvaNodeEvent = {}));
