{"name": "seroval", "type": "module", "version": "1.3.2", "files": ["dist", "src"], "engines": {"node": ">=10"}, "license": "MIT", "keywords": ["pridepack"], "devDependencies": {"@types/node": "^22.15.12", "@vitest/ui": "^3.1.3", "pridepack": "2.6.4", "ts-prune": "^0.10.3", "tslib": "^2.8.1", "typescript": "^5.8.3", "vitest": "^3.1.3"}, "scripts": {"prepublishOnly": "pridepack clean && pridepack build", "build": "pridepack build", "type-check": "pridepack check", "clean": "pridepack clean", "watch": "pridepack watch", "start": "pridepack start", "dev": "pridepack dev", "test": "vitest", "test:ui": "vitest --ui", "prune": "ts-prune"}, "private": false, "description": "Stringify JS values", "repository": {"url": "https://github.com/lxsmnsyc/seroval.git", "type": "git"}, "homepage": "https://github.com/lxsmnsyc/seroval/tree/main/packages/seroval", "bugs": {"url": "https://github.com/lxsmnsyc/seroval/issues"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "types": "./dist/types/index.d.ts", "main": "./dist/cjs/production/index.cjs", "module": "./dist/esm/production/index.mjs", "exports": {".": {"types": "./dist/types/index.d.ts", "development": {"require": "./dist/cjs/development/index.cjs", "import": "./dist/esm/development/index.mjs"}, "require": "./dist/cjs/production/index.cjs", "import": "./dist/esm/production/index.mjs"}}, "typesVersions": {"*": {}}, "gitHead": "c1e47dd1233aaec1d4fae82c5776c31d5850def9"}