/** `a + b + c + ...` */
export declare function add(...a: number[]): number;
export declare function add(...a: string[]): string;
/** `a - b - c - ...` */
export declare const substract: (a: number, ...b: number[]) => number;
/** `a * b * c * ...` */
export declare const multiply: (a: number, ...b: number[]) => number;
/** `a / b / c / ...` */
export declare const divide: (a: number, ...b: number[]) => number;
/** `a ** b ** c ** ...` */
export declare const power: (a: number, ...b: number[]) => number;
/** clamp a number value between two other values */
export declare const clamp: (n: number, min: number, max: number) => number;
