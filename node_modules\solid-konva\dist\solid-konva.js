import { createRenderEffect as w, sharedConfig as y, untrack as q, batch as U, createSignal as S, onMount as L, $PROXY as X, createEffect as b, on as F, onCleanup as E, $TRACK as G, createContext as k, createComponent as N, useContext as I } from "solid-js";
import P from "konva";
const Y = ["allowfullscreen", "async", "autofocus", "autoplay", "checked", "controls", "default", "disabled", "formnovalidate", "hidden", "indeterminate", "ismap", "loop", "multiple", "muted", "nomodule", "novalidate", "open", "playsinline", "readonly", "required", "reversed", "seamless", "selected"], J = /* @__PURE__ */ new Set(["className", "value", "readOnly", "formNoValidate", "isMap", "noModule", "playsInline", ...Y]), Q = /* @__PURE__ */ new Set(["innerHTML", "textContent", "innerText", "children"]), Z = {
  className: "class",
  htmlFor: "for"
}, T = {
  class: "className",
  formnovalidate: "formNoValidate",
  ismap: "isMap",
  nomodule: "noModule",
  playsinline: "playsInline",
  readonly: "readOnly"
}, v = /* @__PURE__ */ new Set(["beforeinput", "click", "dblclick", "contextmenu", "focusin", "focusout", "input", "keydown", "keyup", "mousedown", "mousemove", "mouseout", "mouseover", "mouseup", "pointerdown", "pointermove", "pointerout", "pointerover", "pointerup", "touchend", "touchmove", "touchstart"]), p = {
  xlink: "http://www.w3.org/1999/xlink",
  xml: "http://www.w3.org/XML/1998/namespace"
};
function ee(t, n, e) {
  let s = e.length, o = n.length, l = s, f = 0, i = 0, r = n[o - 1].nextSibling, c = null;
  for (; f < o || i < l; ) {
    if (n[f] === e[i]) {
      f++, i++;
      continue;
    }
    for (; n[o - 1] === e[l - 1]; )
      o--, l--;
    if (o === f) {
      const u = l < s ? i ? e[i - 1].nextSibling : e[l - i] : r;
      for (; i < l; )
        t.insertBefore(e[i++], u);
    } else if (l === i)
      for (; f < o; )
        (!c || !c.has(n[f])) && n[f].remove(), f++;
    else if (n[f] === e[l - 1] && e[i] === n[o - 1]) {
      const u = n[--o].nextSibling;
      t.insertBefore(e[i++], n[f++].nextSibling), t.insertBefore(e[--l], u), n[o] = e[l];
    } else {
      if (!c) {
        c = /* @__PURE__ */ new Map();
        let a = i;
        for (; a < l; )
          c.set(e[a], a++);
      }
      const u = c.get(n[f]);
      if (u != null)
        if (i < u && u < l) {
          let a = f, h = 1, g;
          for (; ++a < o && a < l && !((g = c.get(n[a])) == null || g !== u + h); )
            h++;
          if (h > u - i) {
            const C = n[f];
            for (; i < u; )
              t.insertBefore(e[i++], C);
          } else
            t.replaceChild(e[i++], n[f++]);
        } else
          f++;
      else
        n[f++].remove();
    }
  }
}
const $ = "_$DX_DELEGATE";
function te(t, n, e) {
  const s = document.createElement("template");
  s.innerHTML = t;
  let o = s.content.firstChild;
  return e && (o = o.firstChild), o;
}
function ne(t, n = window.document) {
  const e = n[$] || (n[$] = /* @__PURE__ */ new Set());
  for (let s = 0, o = t.length; s < o; s++) {
    const l = t[s];
    e.has(l) || (e.add(l), n.addEventListener(l, ae));
  }
}
function ie(t, n, e) {
  e == null ? t.removeAttribute(n) : t.setAttribute(n, e);
}
function se(t, n, e, s) {
  s == null ? t.removeAttributeNS(n, e) : t.setAttributeNS(n, e, s);
}
function oe(t, n) {
  n == null ? t.removeAttribute("class") : t.className = n;
}
function fe(t, n, e, s) {
  if (s)
    Array.isArray(e) ? (t[`$$${n}`] = e[0], t[`$$${n}Data`] = e[1]) : t[`$$${n}`] = e;
  else if (Array.isArray(e)) {
    const o = e[0];
    t.addEventListener(n, e[0] = (l) => o.call(t, e[1], l));
  } else
    t.addEventListener(n, e);
}
function le(t, n, e = {}) {
  const s = Object.keys(n || {}), o = Object.keys(e);
  let l, f;
  for (l = 0, f = o.length; l < f; l++) {
    const i = o[l];
    !i || i === "undefined" || n[i] || (R(t, i, !1), delete e[i]);
  }
  for (l = 0, f = s.length; l < f; l++) {
    const i = s[l], r = !!n[i];
    !i || i === "undefined" || e[i] === r || !r || (R(t, i, !0), e[i] = r);
  }
  return e;
}
function ce(t, n, e = {}) {
  const s = t.style, o = typeof e == "string";
  if (n == null && o || typeof n == "string")
    return s.cssText = n;
  o && (s.cssText = void 0, e = {}), n || (n = {});
  let l, f;
  for (f in e)
    n[f] == null && s.removeProperty(f), delete e[f];
  for (f in n)
    l = n[f], l !== e[f] && (s.setProperty(f, l), e[f] = l);
  return e;
}
function re(t, n, e, s) {
  typeof n == "function" ? w((o) => M(t, n(), o, e, s)) : M(t, n, void 0, e, s);
}
function K(t, n, e, s) {
  if (e !== void 0 && !s && (s = []), typeof n != "function")
    return A(t, n, s, e);
  w((o) => A(t, n(), o, e), s);
}
function ue(t, n, e, s, o = {}, l = !1) {
  n || (n = {});
  for (const f in o)
    if (!(f in n)) {
      if (f === "children")
        continue;
      O(t, f, null, o[f], e, l);
    }
  for (const f in n) {
    if (f === "children") {
      s || A(t, n.children);
      continue;
    }
    const i = n[f];
    o[f] = O(t, f, i, o[f], e, l);
  }
}
function de(t) {
  return t.toLowerCase().replace(/-([a-z])/g, (n, e) => e.toUpperCase());
}
function R(t, n, e) {
  const s = n.trim().split(/\s+/);
  for (let o = 0, l = s.length; o < l; o++)
    t.classList.toggle(s[o], e);
}
function O(t, n, e, s, o, l) {
  let f, i, r;
  if (n === "style")
    return ce(t, e, s);
  if (n === "classList")
    return le(t, e, s);
  if (e === s)
    return s;
  if (n === "ref")
    l || e(t);
  else if (n.slice(0, 3) === "on:") {
    const c = n.slice(3);
    s && t.removeEventListener(c, s), e && t.addEventListener(c, e);
  } else if (n.slice(0, 10) === "oncapture:") {
    const c = n.slice(10);
    s && t.removeEventListener(c, s, !0), e && t.addEventListener(c, e, !0);
  } else if (n.slice(0, 2) === "on") {
    const c = n.slice(2).toLowerCase(), u = v.has(c);
    if (!u && s) {
      const a = Array.isArray(s) ? s[0] : s;
      t.removeEventListener(c, a);
    }
    (u || e) && (fe(t, c, e, u), u && ne([c]));
  } else if ((r = Q.has(n)) || !o && (T[n] || (i = J.has(n))) || (f = t.nodeName.includes("-")))
    n === "class" || n === "className" ? oe(t, e) : f && !i && !r ? t[de(n)] = e : t[T[n] || n] = e;
  else {
    const c = o && n.indexOf(":") > -1 && p[n.split(":")[0]];
    c ? se(t, c, n, e) : ie(t, Z[n] || n, e);
  }
  return e;
}
function ae(t) {
  const n = `$$${t.type}`;
  let e = t.composedPath && t.composedPath()[0] || t.target;
  for (t.target !== e && Object.defineProperty(t, "target", {
    configurable: !0,
    value: e
  }), Object.defineProperty(t, "currentTarget", {
    configurable: !0,
    get() {
      return e || document;
    }
  }), y.registry && !y.done && (y.done = !0, document.querySelectorAll("[id^=pl-]").forEach((s) => s.remove())); e !== null; ) {
    const s = e[n];
    if (s && !e.disabled) {
      const o = e[`${n}Data`];
      if (o !== void 0 ? s.call(e, o, t) : s.call(e, t), t.cancelBubble)
        return;
    }
    e = e.host && e.host !== e && e.host instanceof Node ? e.host : e.parentNode;
  }
}
function M(t, n, e = {}, s, o) {
  return n || (n = {}), !o && "children" in n && w(() => e.children = A(t, n.children, e.children)), n.ref && n.ref(t), w(() => ue(t, n, s, !0, e, !0)), e;
}
function A(t, n, e, s, o) {
  for (y.context && !e && (e = [...t.childNodes]); typeof e == "function"; )
    e = e();
  if (n === e)
    return e;
  const l = typeof n, f = s !== void 0;
  if (t = f && e[0] && e[0].parentNode || t, l === "string" || l === "number") {
    if (y.context)
      return e;
    if (l === "number" && (n = n.toString()), f) {
      let i = e[0];
      i && i.nodeType === 3 ? i.data = n : i = document.createTextNode(n), e = m(t, e, s, i);
    } else
      e !== "" && typeof e == "string" ? e = t.firstChild.data = n : e = t.textContent = n;
  } else if (n == null || l === "boolean") {
    if (y.context)
      return e;
    e = m(t, e, s);
  } else {
    if (l === "function")
      return w(() => {
        let i = n();
        for (; typeof i == "function"; )
          i = i();
        e = A(t, i, e, s);
      }), () => e;
    if (Array.isArray(n)) {
      const i = [], r = e && Array.isArray(e);
      if (x(i, n, e, o))
        return w(() => e = A(t, i, e, s, !0)), () => e;
      if (y.context) {
        for (let c = 0; c < i.length; c++)
          if (i[c].parentNode)
            return e = i;
      }
      if (i.length === 0) {
        if (e = m(t, e, s), f)
          return e;
      } else
        r ? e.length === 0 ? _(t, i, s) : ee(t, e, i) : (e && m(t), _(t, i));
      e = i;
    } else if (n instanceof Node) {
      if (y.context && n.parentNode)
        return e = f ? [n] : n;
      if (Array.isArray(e)) {
        if (f)
          return e = m(t, e, s, n);
        m(t, e, null, n);
      } else
        e == null || e === "" || !t.firstChild ? t.appendChild(n) : t.replaceChild(n, t.firstChild);
      e = n;
    }
  }
  return e;
}
function x(t, n, e, s) {
  let o = !1;
  for (let l = 0, f = n.length; l < f; l++) {
    let i = n[l], r = e && e[l];
    if (i instanceof Node)
      t.push(i);
    else if (!(i == null || i === !0 || i === !1))
      if (Array.isArray(i))
        o = x(t, i, r) || o;
      else if (typeof i == "function")
        if (s) {
          for (; typeof i == "function"; )
            i = i();
          o = x(t, Array.isArray(i) ? i : [i], r) || o;
        } else
          t.push(i), o = !0;
      else {
        const c = String(i);
        r && r.nodeType === 3 && r.data === c ? t.push(r) : t.push(document.createTextNode(c));
      }
  }
  return o;
}
function _(t, n, e) {
  for (let s = 0, o = n.length; s < o; s++)
    t.insertBefore(n[s], e);
}
function m(t, n, e, s) {
  if (e === void 0)
    return t.textContent = "";
  const o = s || document.createTextNode("");
  if (n.length) {
    let l = !1;
    for (let f = n.length - 1; f >= 0; f--) {
      const i = n[f];
      if (o !== i) {
        const r = i.parentNode === t;
        !l && !f ? r ? t.replaceChild(o, i) : t.insertBefore(o, e) : r && i.remove();
      } else
        l = !0;
    }
  } else
    t.insertBefore(o, e);
  return [o];
}
function he(t) {
  return t !== null && (typeof t == "object" || typeof t == "function");
}
var ge = (t) => typeof t == "function" && !t.length ? t() : t, j = (t) => Array.isArray(t) ? t : [t];
function B(t, ...n) {
  return typeof t == "function" ? t(...n) : t;
}
var ye = Object.entries, me = Object.keys;
function we(t) {
  const n = { ...t }, e = {}, s = /* @__PURE__ */ new Map(), o = (i) => {
    const r = s.get(i);
    if (r)
      return r[0]();
    const c = S(n[i], {
      name: typeof i == "string" ? i : void 0
    });
    return s.set(i, c), delete n[i], c[0]();
  }, l = (i, r) => {
    const c = s.get(i);
    if (c)
      return c[1](r);
    i in n && (n[i] = B(r, [n[i]]));
  };
  for (const i of me(t))
    e[i] = void 0, Object.defineProperty(e, i, {
      get: o.bind(void 0, i)
    });
  return [e, (i, r) => (he(i) ? q(() => {
    U(() => {
      for (const [c, u] of ye(B(i, e)))
        l(c, () => u);
    });
  }) : l(i, r), e)];
}
function be(t, n, e, s) {
  const o = t.length, l = n.length;
  let f = 0;
  if (!l) {
    for (; f < o; f++)
      e(t[f]);
    return;
  }
  if (!o) {
    for (; f < l; f++)
      s(n[f]);
    return;
  }
  for (; f < l && n[f] === t[f]; f++)
    ;
  let i, r;
  n = n.slice(f), t = t.slice(f);
  for (i of n)
    t.includes(i) || s(i);
  for (r of t)
    n.includes(r) || e(r);
}
function Ae(t, n) {
  const e = new ResizeObserver(t);
  return E(e.disconnect.bind(e)), {
    observe: (s) => e.observe(s, n),
    unobserve: e.unobserve.bind(e)
  };
}
function Ce(t, n, e) {
  const s = /* @__PURE__ */ new WeakMap(), { observe: o, unobserve: l } = Ae(f, e);
  function f(r) {
    for (const c of r) {
      const { contentRect: u, target: a } = c, h = Math.round(u.width), g = Math.round(u.height), C = s.get(a);
      (!C || C.width !== h || C.height !== g) && (n(u, c.target, c), s.set(a, { width: h, height: g }));
    }
  }
  let i;
  if (typeof t == "function")
    i = () => j(t()).slice();
  else if (Array.isArray(t) && X in t)
    i = () => (t[G], t.slice());
  else {
    j(t).forEach(o);
    return;
  }
  b(F(i, (r, c = []) => be(r, c, o, l)));
}
function z(t) {
  if (!t)
    return {
      width: null,
      height: null
    };
  const { width: n, height: e } = t.getBoundingClientRect();
  return { width: n, height: e };
}
function Se(t) {
  const [n, e] = we(z(ge(t)));
  return typeof t == "function" && L(() => e(z(t()))), Ce(typeof t == "function" ? () => t() || [] : t, (o) => e({ width: o.width, height: o.height })), n;
}
const H = /* @__PURE__ */ te("<div></div>");
function Ee(t) {
  const [n, e] = S(), s = Se(n), [o, l] = S();
  return L(() => {
    l(new P.Stage({
      height: s.width,
      width: s.height,
      container: n(),
      ...t
    }));
  }), b(() => {
    o()?.setAttrs({
      width: s.width,
      height: s.height
    });
  }), E(() => {
    o()?.destroy();
  }), {
    ...t,
    ref: e,
    containerRef: n,
    stage: o
  };
}
const V = k(null);
function xe(t) {
  return N(V.Provider, {
    get value() {
      return t.stageProps;
    },
    get children() {
      return t.children;
    }
  });
}
function Le() {
  return I(V);
}
function $e(t) {
  const n = Ee({
    ...t
  });
  return (() => {
    const e = H.cloneNode(!0), s = n.ref;
    return typeof s == "function" ? s(e) : n.ref = e, re(e, t, !1, !0), K(e, N(xe, {
      stageProps: n,
      get children() {
        return t.children;
      }
    })), e;
  })();
}
const W = k();
function Ne() {
  return I(W);
}
function Re(t) {
  const n = new P.Layer(t), e = Le();
  return b(() => {
    e?.stage() && e.stage().add(n);
  }), b(() => {
    n.setAttrs(t);
  }), E(() => {
    console.log("murp"), n.destroy();
  }), (() => {
    const s = H.cloneNode(!0);
    return K(s, N(W.Provider, {
      value: {
        layer: n
      },
      get children() {
        return t.children;
      }
    })), s;
  })();
}
const D = {
  children: !0,
  ref: !0,
  key: !0,
  style: !0,
  forwardedRef: !0,
  unstable_applyCache: !0,
  unstable_applyDrawHitFromCache: !0
};
function d(t) {
  function n(e) {
    let s = {};
    const [o, l] = S(null), f = Ne();
    return L(() => {
      const i = new P[t](e);
      l(i), f?.layer?.add(i);
    }), b(() => {
      !o() || o().setAttrs(e);
    }), b(() => {
      if (!o())
        return;
      if (s)
        for (const c in s) {
          if (D[c])
            continue;
          const u = c.slice(0, 2) === "on", a = s[c] !== e[c];
          if (u && a) {
            let g = c.substring(2).toLowerCase();
            g.substring(0, 7) === "content" && (g = "content" + g.substring(7, 1).toUpperCase() + g.substring(8)), o().off(g, s[c]);
          }
          !e.hasOwnProperty(c) && o().setAttr(c, void 0);
        }
      const i = {};
      for (const c in e) {
        if (D[c])
          continue;
        const u = c.slice(0, 2) === "on", a = s[c] !== e[c];
        if (u && a) {
          let h = c.substring(2).toLowerCase();
          h.substring(0, 7) === "content" && (h = "content" + h.substring(7, 1).toUpperCase() + h.substring(8)), e[c] && (i[h] = e[c]);
        }
      }
      for (var r in i)
        o()?.on(r, i[r]);
      s = e;
    }), E(() => {
      o().destroy(), console.log("destroyed");
    }), [];
  }
  return n;
}
const Oe = d("Group"), Me = d("Rect"), _e = d("Circle"), je = d("Ellipse"), Be = d("Wedge"), ze = d("Line"), De = d("Sprite"), ke = d("Image"), Ie = d("Text"), Ke = d("TextPath"), He = d("Star"), Ve = d("Ring"), We = d("Arc"), qe = d("Tag"), Ue = d("Path"), Xe = d("RegularPolygon"), Fe = d("Arrow"), Ge = d("Shape"), Ye = d("Transformer");
export {
  We as Arc,
  Fe as Arrow,
  _e as Circle,
  je as Ellipse,
  Oe as Group,
  ke as Image,
  Re as Layer,
  ze as Line,
  Ue as Path,
  Me as Rect,
  Xe as RegularPolygon,
  Ve as Ring,
  Ge as Shape,
  De as Sprite,
  $e as Stage,
  xe as StageContextProvider,
  He as Star,
  qe as Tag,
  Ie as Text,
  Ke as TextPath,
  Ye as Transformer,
  Be as Wedge,
  Le as useStage
};
