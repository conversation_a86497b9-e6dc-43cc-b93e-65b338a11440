{"version": 3, "sources": ["../../../src/index.ts", "../../../src/core/compat.ts", "../../../src/core/string.ts", "../../../src/core/keys.ts", "../../../src/core/utils/assert.ts", "../../../src/core/reference.ts", "../../../src/core/plugin.ts", "../../../src/core/constants.ts", "../../../src/core/node.ts", "../../../src/core/literals.ts", "../../../src/core/utils/error.ts", "../../../src/core/utils/get-object-flag.ts", "../../../src/core/base-primitives.ts", "../../../src/core/errors.ts", "../../../src/core/opaque-reference.ts", "../../../src/core/function-string.ts", "../../../src/core/special-reference.ts", "../../../src/core/utils/deferred.ts", "../../../src/core/stream.ts", "../../../src/core/utils/iterator-to-sequence.ts", "../../../src/core/utils/promise-to-result.ts", "../../../src/core/context/parser.ts", "../../../src/core/context/parser/async.ts", "../../../src/core/cross/async.ts", "../../../src/core/utils/typed-array.ts", "../../../src/core/context/deserializer.ts", "../../../src/core/cross/deserializer.ts", "../../../src/core/utils/is-valid-identifier.ts", "../../../src/core/context/serializer.ts", "../../../src/core/cross/serializer.ts", "../../../src/core/context/parser/sync.ts", "../../../src/core/context/parser/stream.ts", "../../../src/core/cross/stream.ts", "../../../src/core/cross/sync.ts", "../../../src/core/cross/index.ts", "../../../src/core/tree/async.ts", "../../../src/core/tree/deserializer.ts", "../../../src/core/utils/get-identifier.ts", "../../../src/core/tree/serializer.ts", "../../../src/core/tree/sync.ts", "../../../src/core/tree/index.ts", "../../../src/core/Serializer.ts"], "sourcesContent": ["export { Feature } from './core/compat';\nexport { createReference } from './core/reference';\n\nexport * from './core/cross';\nexport * from './core/tree';\n\nexport { getCrossReferenceHeader } from './core/keys';\n\nexport * from './core/plugin';\nexport { default as Serializer } from './core/Serializer';\n\nexport { createStream } from './core/stream';\nexport type { Stream } from './core/stream';\n\nexport * from './core/errors';\nexport type { SerovalNode } from './core/types';\n\nexport { OpaqueReference } from './core/opaque-reference';\n", "/**\n * References\n * - https://compat-table.github.io/compat-table/es6/\n * - MDN\n */\n\nexport const enum Feature {\n  AggregateError = 0x01,\n  ArrowFunction = 0x02,\n  ErrorPrototypeStack = 0x04,\n  ObjectAssign = 0x08,\n  BigIntTypedArray = 0x10,\n}\n\nexport const ALL_ENABLED =\n  Feature.AggregateError |\n  Feature.ArrowFunction |\n  Feature.ErrorPrototypeStack |\n  Feature.ObjectAssign |\n  Feature.BigIntTypedArray;\n", "export function serializeChar(str: string): string | undefined {\n  switch (str) {\n    case '\"':\n      return '\\\\\"';\n    case '\\\\':\n      return '\\\\\\\\';\n    case '\\n':\n      return '\\\\n';\n    case '\\r':\n      return '\\\\r';\n    case '\\b':\n      return '\\\\b';\n    case '\\t':\n      return '\\\\t';\n    case '\\f':\n      return '\\\\f';\n    case '<':\n      return '\\\\x3C';\n    case '\\u2028':\n      return '\\\\u2028';\n    case '\\u2029':\n      return '\\\\u2029';\n    default:\n      return undefined;\n  }\n}\n\n// Written by https://github.com/DylanPiercey and is distributed under the MIT license.\n// Creates a JavaScript double quoted string and escapes all characters\n// not listed as DoubleStringCharacters on\n// Also includes \"<\" to escape \"</script>\" and \"\\\" to avoid invalid escapes in the output.\n// http://www.ecma-international.org/ecma-262/5.1/#sec-7.8.4\nexport function serializeString(str: string): string {\n  let result = '';\n  let lastPos = 0;\n  let replacement: string | undefined;\n  for (let i = 0, len = str.length; i < len; i++) {\n    replacement = serializeChar(str[i]);\n    if (replacement) {\n      result += str.slice(lastPos, i) + replacement;\n      lastPos = i + 1;\n    }\n  }\n  if (lastPos === 0) {\n    result = str;\n  } else {\n    result += str.slice(lastPos);\n  }\n  return result;\n}\n\nfunction deserializeReplacer(str: string): string {\n  switch (str) {\n    case '\\\\\\\\':\n      return '\\\\';\n    case '\\\\\"':\n      return '\"';\n    case '\\\\n':\n      return '\\n';\n    case '\\\\r':\n      return '\\r';\n    case '\\\\b':\n      return '\\b';\n    case '\\\\t':\n      return '\\t';\n    case '\\\\f':\n      return '\\f';\n    case '\\\\x3C':\n      return '\\x3C';\n    case '\\\\u2028':\n      return '\\u2028';\n    case '\\\\u2029':\n      return '\\u2029';\n    default:\n      return str;\n  }\n}\n\nexport function deserializeString(str: string): string {\n  return str.replace(\n    /(\\\\\\\\|\\\\\"|\\\\n|\\\\r|\\\\b|\\\\t|\\\\f|\\\\u2028|\\\\u2029|\\\\x3C)/g,\n    deserializeReplacer,\n  );\n}\n", "import { serializeString } from './string';\n\n// Used for mapping isomorphic references\nexport const REFERENCES_KEY = '__SEROVAL_REFS__';\n\nexport const GLOBAL_CONTEXT_REFERENCES = '$R';\n\nconst GLOBAL_CONTEXT_R = `self.${GLOBAL_CONTEXT_REFERENCES}`;\n\nexport function getCrossReferenceHeader(id?: string): string {\n  if (id == null) {\n    return `${GLOBAL_CONTEXT_R}=${GLOBAL_CONTEXT_R}||[]`;\n  }\n  return `(${GLOBAL_CONTEXT_R}=${GLOBAL_CONTEXT_R}||{})[\"${serializeString(\n    id,\n  )}\"]=[]`;\n}\n", "export default function assert(cond: unknown, error: Error): asserts cond {\n  if (!cond) {\n    throw error;\n  }\n}\n", "import {\n  SerovalMissingReferenceError,\n  SerovalMissingReferenceForIdError,\n} from '..';\nimport { REFERENCES_KEY } from './keys';\nimport assert from './utils/assert';\n\nconst REFERENCE = new Map<unknown, string>();\nconst INV_REFERENCE = new Map<string, unknown>();\n\nexport function createReference<T>(id: string, value: T): T {\n  REFERENCE.set(value, id);\n  INV_REFERENCE.set(id, value);\n  return value;\n}\n\nexport function hasReferenceID<T>(value: T): boolean {\n  return REFERENCE.has(value);\n}\n\nexport function hasReference(id: string): boolean {\n  return INV_REFERENCE.has(id);\n}\n\nexport function getReferenceID<T>(value: T): string {\n  assert(hasReferenceID(value), new SerovalMissingReferenceError(value));\n  return REFERENCE.get(value)!;\n}\n\nexport function getReference<T>(id: string): T {\n  assert(hasReference(id), new SerovalMissingReferenceForIdError(id));\n  return INV_REFERENCE.get(id) as T;\n}\n\nif (typeof globalThis !== 'undefined') {\n  Object.defineProperty(globalThis, REFERENCES_KEY, {\n    value: INV_REFERENCE,\n    configurable: true,\n    writable: false,\n    enumerable: false,\n  });\n} else if (typeof window !== 'undefined') {\n  Object.defineProperty(window, REFERENCES_KEY, {\n    value: INV_REFERENCE,\n    configurable: true,\n    writable: false,\n    enumerable: false,\n  });\n} else if (typeof self !== 'undefined') {\n  Object.defineProperty(self, REFERENCES_KEY, {\n    value: INV_REFERENCE,\n    configurable: true,\n    writable: false,\n    enumerable: false,\n  });\n} else if (typeof global !== 'undefined') {\n  Object.defineProperty(global, REFERENCES_KEY, {\n    value: INV_REFERENCE,\n    configurable: true,\n    writable: false,\n    enumerable: false,\n  });\n}\n", "import type BaseDeserializerContext from './context/deserializer';\nimport type BaseAsyncParserContext from './context/parser/async';\nimport type BaseStreamParserContext from './context/parser/stream';\nimport type BaseSyncParserContext from './context/parser/sync';\nimport type BaseSerializerContext from './context/serializer';\n\nexport type SerovalMode = 'vanilla' | 'cross';\n\nexport interface PluginData {\n  id: number;\n}\n\nexport interface Plugin<Value, Node> {\n  /**\n   * A unique string that helps idenfity the plugin\n   */\n  tag: string;\n  /**\n   * List of dependency plugins\n   */\n  extends?: Plugin<any, any>[];\n  /**\n   * Method to test if a value is an expected value of the plugin\n   * @param value\n   */\n  test(value: unknown): boolean;\n  /**\n   * Parsing modes\n   */\n  parse: {\n    sync?: (value: Value, ctx: BaseSyncParserContext, data: PluginData) => Node;\n    async?: (\n      value: Value,\n      ctx: BaseAsyncParserContext,\n      data: PluginData,\n    ) => Promise<Node>;\n    stream?: (\n      value: Value,\n      ctx: BaseStreamParserContext,\n      data: PluginData,\n    ) => Node;\n  };\n  /**\n   * Convert the parsed node into a JS string\n   */\n  serialize(node: Node, ctx: BaseSerializerContext, data: PluginData): string;\n  /**\n   * Convert the parsed node into its runtime equivalent.\n   */\n  deserialize(\n    node: Node,\n    ctx: BaseDeserializerContext,\n    data: PluginData,\n  ): Value;\n}\n\nexport function createPlugin<Value, Node>(\n  plugin: Plugin<Value, Node>,\n): Plugin<Value, Node> {\n  return plugin;\n}\n\nexport interface PluginAccessOptions {\n  plugins?: Plugin<any, any>[];\n}\n\nfunction dedupePlugins(\n  deduped: Set<Plugin<any, any>>,\n  plugins: Plugin<any, any>[],\n): void {\n  for (let i = 0, len = plugins.length; i < len; i++) {\n    const current = plugins[i];\n    if (!deduped.has(current)) {\n      deduped.add(current);\n      if (current.extends) {\n        dedupePlugins(deduped, current.extends);\n      }\n    }\n  }\n}\n\nexport function resolvePlugins(\n  plugins?: Plugin<any, any>[],\n): Plugin<any, any>[] | undefined {\n  if (plugins) {\n    const deduped = new Set<Plugin<any, any>>();\n    dedupePlugins(deduped, plugins);\n    return [...deduped];\n  }\n  return undefined;\n}\n", "export const enum SerovalConstant {\n  Null = 0,\n  Undefined = 1,\n  True = 2,\n  False = 3,\n  NegZero = 4,\n  Inf = 5,\n  NegInf = 6,\n  <PERSON> = 7,\n}\n\nexport const enum SerovalNodeType {\n  Number = 0,\n  String = 1,\n  Constant = 2,\n  BigInt = 3,\n  IndexedValue = 4,\n  Date = 5,\n  RegExp = 6,\n  Set = 7,\n  Map = 8,\n  Array = 9,\n  Object = 10,\n  NullConstructor = 11,\n  Promise = 12,\n  Error = 13,\n  AggregateError = 14,\n  TypedArray = 15,\n  BigIntTypedArray = 16,\n  WKSymbol = 17,\n  Reference = 18,\n  ArrayBuffer = 19,\n  DataView = 20,\n  Boxed = 21,\n  PromiseConstructor = 22,\n  PromiseSuccess = 23,\n  PromiseFailure = 24,\n  Plugin = 25,\n  SpecialReference = 26,\n  IteratorFactory = 27,\n  IteratorFactoryInstance = 28,\n  AsyncIteratorFactory = 29,\n  AsyncIteratorFactoryInstance = 30,\n  StreamConstructor = 31,\n  StreamNext = 32,\n  StreamThrow = 33,\n  StreamReturn = 34,\n}\n\nexport const enum SerovalObjectFlags {\n  None = 0,\n  NonExtensible = 1,\n  Sealed = 2,\n  Frozen = 3,\n}\n\nexport const enum Symbols {\n  AsyncIterator = 0,\n  HasInstance = 1,\n  IsConcatSpreadable = 2,\n  Iterator = 3,\n  Match = 4,\n  MatchAll = 5,\n  Replace = 6,\n  Search = 7,\n  Species = 8,\n  Split = 9,\n  ToPrimitive = 10,\n  ToStringTag = 11,\n  Unscopables = 12,\n}\n\nexport const SYMBOL_STRING: Record<Symbols, string> = {\n  [Symbols.AsyncIterator]: 'Symbol.asyncIterator',\n  [Symbols.HasInstance]: 'Symbol.hasInstance',\n  [Symbols.IsConcatSpreadable]: 'Symbol.isConcatSpreadable',\n  [Symbols.Iterator]: 'Symbol.iterator',\n  [Symbols.Match]: 'Symbol.match',\n  [Symbols.MatchAll]: 'Symbol.matchAll',\n  [Symbols.Replace]: 'Symbol.replace',\n  [Symbols.Search]: 'Symbol.search',\n  [Symbols.Species]: 'Symbol.species',\n  [Symbols.Split]: 'Symbol.split',\n  [Symbols.ToPrimitive]: 'Symbol.toPrimitive',\n  [Symbols.ToStringTag]: 'Symbol.toStringTag',\n  [Symbols.Unscopables]: 'Symbol.unscopables',\n};\n\nexport const INV_SYMBOL_REF = /* @__PURE__ */ {\n  [Symbol.asyncIterator]: Symbols.AsyncIterator,\n  [Symbol.hasInstance]: Symbols.HasInstance,\n  [Symbol.isConcatSpreadable]: Symbols.IsConcatSpreadable,\n  [Symbol.iterator]: Symbols.Iterator,\n  [Symbol.match]: Symbols.Match,\n  [Symbol.matchAll]: Symbols.MatchAll,\n  [Symbol.replace]: Symbols.Replace,\n  [Symbol.search]: Symbols.Search,\n  [Symbol.species]: Symbols.Species,\n  [Symbol.split]: Symbols.Split,\n  [Symbol.toPrimitive]: Symbols.ToPrimitive,\n  [Symbol.toStringTag]: Symbols.ToStringTag,\n  [Symbol.unscopables]: Symbols.Unscopables,\n};\n\nexport type WellKnownSymbols = keyof typeof INV_SYMBOL_REF;\n\nexport const SYMBOL_REF: Record<Symbols, WellKnownSymbols> = {\n  [Symbols.AsyncIterator]: Symbol.asyncIterator,\n  [Symbols.HasInstance]: Symbol.hasInstance,\n  [Symbols.IsConcatSpreadable]: Symbol.isConcatSpreadable,\n  [Symbols.Iterator]: Symbol.iterator,\n  [Symbols.Match]: Symbol.match,\n  [Symbols.MatchAll]: Symbol.matchAll,\n  [Symbols.Replace]: Symbol.replace,\n  [Symbols.Search]: Symbol.search,\n  [Symbols.Species]: Symbol.species,\n  [Symbols.Split]: Symbol.split,\n  [Symbols.ToPrimitive]: Symbol.toPrimitive,\n  [Symbols.ToStringTag]: Symbol.toStringTag,\n  [Symbols.Unscopables]: Symbol.unscopables,\n};\n\nexport const CONSTANT_STRING: Record<SerovalConstant, string> = {\n  [SerovalConstant.True]: '!0',\n  [SerovalConstant.False]: '!1',\n  [SerovalConstant.Undefined]: 'void 0',\n  [SerovalConstant.Null]: 'null',\n  [SerovalConstant.NegZero]: '-0',\n  [SerovalConstant.Inf]: '1/0',\n  [SerovalConstant.NegInf]: '-1/0',\n  [SerovalConstant.Nan]: '0/0',\n};\n\nexport const CONSTANT_VAL: Record<SerovalConstant, unknown> = {\n  [SerovalConstant.True]: true,\n  [SerovalConstant.False]: false,\n  [SerovalConstant.Undefined]: undefined,\n  [SerovalConstant.Null]: null,\n  [SerovalConstant.NegZero]: -0,\n  [SerovalConstant.Inf]: Number.POSITIVE_INFINITY,\n  [SerovalConstant.NegInf]: Number.NEGATIVE_INFINITY,\n  [SerovalConstant.Nan]: Number.NaN,\n};\n\nexport const enum ErrorConstructorTag {\n  Error = 0,\n  EvalError = 1,\n  RangeError = 2,\n  ReferenceError = 3,\n  SyntaxError = 4,\n  TypeError = 5,\n  URIError = 6,\n}\n\nexport const ERROR_CONSTRUCTOR_STRING: Record<ErrorConstructorTag, string> = {\n  [ErrorConstructorTag.Error]: 'Error',\n  [ErrorConstructorTag.EvalError]: 'EvalError',\n  [ErrorConstructorTag.RangeError]: 'RangeError',\n  [ErrorConstructorTag.ReferenceError]: 'ReferenceError',\n  [ErrorConstructorTag.SyntaxError]: 'SyntaxError',\n  [ErrorConstructorTag.TypeError]: 'TypeError',\n  [ErrorConstructorTag.URIError]: 'URIError',\n};\n\ntype ErrorConstructors =\n  | ErrorConstructor\n  | EvalErrorConstructor\n  | RangeErrorConstructor\n  | ReferenceErrorConstructor\n  | SyntaxErrorConstructor\n  | TypeErrorConstructor\n  | URIErrorConstructor;\n\nexport const ERROR_CONSTRUCTOR: Record<ErrorConstructorTag, ErrorConstructors> =\n  {\n    [ErrorConstructorTag.Error]: Error,\n    [ErrorConstructorTag.EvalError]: EvalError,\n    [ErrorConstructorTag.RangeError]: RangeError,\n    [ErrorConstructorTag.ReferenceError]: ReferenceError,\n    [ErrorConstructorTag.SyntaxError]: SyntaxError,\n    [ErrorConstructorTag.TypeError]: TypeError,\n    [ErrorConstructorTag.URIError]: URIError,\n  };\n\nexport const NIL = undefined;\n", "import type { SerovalNodeType } from './constants';\nimport type { SerovalNode } from './types';\n\ntype ExtractedNodeType<T extends SerovalNodeType> = Extract<\n  SerovalNode,\n  { t: T }\n>;\n\nexport function createSerovalNode<\n  T extends SerovalNodeType,\n  N extends ExtractedNodeType<T>,\n>(\n  t: T,\n  i: N['i'],\n  s: N['s'],\n  l: N['l'],\n  c: N['c'],\n  m: N['m'],\n  p: N['p'],\n  e: N['e'],\n  a: N['a'],\n  f: N['f'],\n  b: N['b'],\n  o: N['o'],\n): N {\n  return {\n    t,\n    i,\n    s,\n    l,\n    c,\n    m,\n    p,\n    e,\n    a,\n    f,\n    b,\n    o,\n  } as N;\n}\n", "import { NIL, SerovalConstant, SerovalNodeType } from './constants';\nimport { createSerovalNode } from './node';\nimport type { SerovalConstantNode } from './types';\n\nfunction createConstantNode(value: SerovalConstant): SerovalConstantNode {\n  return createSerovalNode(\n    SerovalNodeType.Constant,\n    NIL,\n    value,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport const TRUE_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.True,\n);\nexport const FALSE_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.False,\n);\nexport const UNDEFINED_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.Undefined,\n);\nexport const NULL_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.Null,\n);\nexport const NEG_ZERO_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.NegZero,\n);\nexport const INFINITY_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.Inf,\n);\nexport const NEG_INFINITY_NODE = /* @__PURE__ */ createConstantNode(\n  SerovalConstant.NegInf,\n);\nexport const NAN_NODE = /* @__PURE__ */ createConstantNode(SerovalConstant.Nan);\n", "import { Feature } from '../compat';\nimport { ERROR_CONSTRUCTOR_STRING, ErrorConstructorTag } from '../constants';\n\ntype ErrorValue =\n  | Error\n  | AggregateError\n  | EvalError\n  | RangeError\n  | ReferenceError\n  | TypeError\n  | SyntaxError\n  | URIError;\n\nexport function getErrorConstructor(error: ErrorValue): ErrorConstructorTag {\n  if (error instanceof EvalError) {\n    return ErrorConstructorTag.EvalError;\n  }\n  if (error instanceof RangeError) {\n    return ErrorConstructorTag.RangeError;\n  }\n  if (error instanceof ReferenceError) {\n    return ErrorConstructorTag.ReferenceError;\n  }\n  if (error instanceof SyntaxError) {\n    return ErrorConstructorTag.SyntaxError;\n  }\n  if (error instanceof TypeError) {\n    return ErrorConstructorTag.TypeError;\n  }\n  if (error instanceof URIError) {\n    return ErrorConstructorTag.URIError;\n  }\n  return ErrorConstructorTag.Error;\n}\n\nfunction getInitialErrorOptions(\n  error: Error,\n): Record<string, unknown> | undefined {\n  const construct = ERROR_CONSTRUCTOR_STRING[getErrorConstructor(error)];\n  // Name has been modified\n  if (error.name !== construct) {\n    return { name: error.name };\n  }\n  if (error.constructor.name !== construct) {\n    // Otherwise, name is overriden because\n    // the Error class is extended\n    return { name: error.constructor.name };\n  }\n  return {};\n}\n\nexport function getErrorOptions(\n  error: Error,\n  features: number,\n): Record<string, unknown> | undefined {\n  let options = getInitialErrorOptions(error);\n  const names = Object.getOwnPropertyNames(error);\n  for (let i = 0, len = names.length, name: string; i < len; i++) {\n    name = names[i];\n    if (name !== 'name' && name !== 'message') {\n      if (name === 'stack') {\n        if (features & Feature.ErrorPrototypeStack) {\n          options = options || {};\n          options[name] = error[name as keyof Error];\n        }\n      } else {\n        options = options || {};\n        options[name] = error[name as keyof Error];\n      }\n    }\n  }\n  return options;\n}\n", "import { SerovalObjectFlags } from '../constants';\n\nexport function getObjectFlag(obj: unknown): SerovalObjectFlags {\n  if (Object.isFrozen(obj)) {\n    return SerovalObjectFlags.Frozen;\n  }\n  if (Object.isSealed(obj)) {\n    return SerovalObjectFlags.Sealed;\n  }\n  if (Object.isExtensible(obj)) {\n    return SerovalObjectFlags.None;\n  }\n  return SerovalObjectFlags.NonExtensible;\n}\n", "import type { WellKnownSymbols } from './constants';\nimport { INV_SYMBOL_REF, NIL, SerovalNodeType } from './constants';\nimport {\n  INFINITY_NODE,\n  NAN_NODE,\n  NEG_INFINITY_NODE,\n  NEG_ZERO_NODE,\n} from './literals';\nimport { createSerovalNode } from './node';\nimport { getReferenceID } from './reference';\nimport { serializeString } from './string';\nimport type {\n  SerovalAggregateErrorNode,\n  SerovalArrayBufferNode,\n  SerovalArrayNode,\n  SerovalAsyncIteratorFactoryInstanceNode,\n  SerovalBigIntNode,\n  SerovalBigIntTypedArrayNode,\n  SerovalBoxedNode,\n  SerovalConstantNode,\n  SerovalDataViewNode,\n  SerovalDateNode,\n  SerovalErrorNode,\n  SerovalIndexedValueNode,\n  SerovalIteratorFactoryInstanceNode,\n  SerovalNode,\n  SerovalNodeWithID,\n  SerovalNumberNode,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalReferenceNode,\n  SerovalRegExpNode,\n  SerovalSetNode,\n  SerovalStreamConstructorNode,\n  SerovalStreamNextNode,\n  SerovalStreamReturnNode,\n  SerovalStreamThrowNode,\n  SerovalStringNode,\n  SerovalTypedArrayNode,\n  SerovalWKSymbolNode,\n} from './types';\nimport { getErrorConstructor } from './utils/error';\nimport { getObjectFlag } from './utils/get-object-flag';\nimport type {\n  BigIntTypedArrayValue,\n  TypedArrayValue,\n} from './utils/typed-array';\n\nexport function createNumberNode(\n  value: number,\n): SerovalConstantNode | SerovalNumberNode {\n  switch (value) {\n    case Number.POSITIVE_INFINITY:\n      return INFINITY_NODE;\n    case Number.NEGATIVE_INFINITY:\n      return NEG_INFINITY_NODE;\n  }\n  if (value !== value) {\n    return NAN_NODE;\n  }\n  if (Object.is(value, -0)) {\n    return NEG_ZERO_NODE;\n  }\n  return createSerovalNode(\n    SerovalNodeType.Number,\n    NIL,\n    value,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createStringNode(value: string): SerovalStringNode {\n  return createSerovalNode(\n    SerovalNodeType.String,\n    NIL,\n    serializeString(value),\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createBigIntNode(current: bigint): SerovalBigIntNode {\n  return createSerovalNode(\n    SerovalNodeType.BigInt,\n    NIL,\n    '' + current,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createIndexedValueNode(id: number): SerovalIndexedValueNode {\n  return createSerovalNode(\n    SerovalNodeType.IndexedValue,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createDateNode(id: number, current: Date): SerovalDateNode {\n  const timestamp = current.valueOf();\n  return createSerovalNode(\n    SerovalNodeType.Date,\n    id,\n    timestamp !== timestamp ? '' : current.toISOString(),\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createRegExpNode(\n  id: number,\n  current: RegExp,\n): SerovalRegExpNode {\n  return createSerovalNode(\n    SerovalNodeType.RegExp,\n    id,\n    NIL,\n    NIL,\n    serializeString(current.source),\n    current.flags,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createArrayBufferNode(\n  id: number,\n  current: ArrayBuffer,\n): SerovalArrayBufferNode {\n  const bytes = new Uint8Array(current);\n  const len = bytes.length;\n  const values = new Array<number>(len);\n  for (let i = 0; i < len; i++) {\n    values[i] = bytes[i];\n  }\n  return createSerovalNode(\n    SerovalNodeType.ArrayBuffer,\n    id,\n    values,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createWKSymbolNode(\n  id: number,\n  current: WellKnownSymbols,\n): SerovalWKSymbolNode {\n  return createSerovalNode(\n    SerovalNodeType.WKSymbol,\n    id,\n    INV_SYMBOL_REF[current],\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createReferenceNode<T>(\n  id: number,\n  ref: T,\n): SerovalReferenceNode {\n  return createSerovalNode(\n    SerovalNodeType.Reference,\n    id,\n    serializeString(getReferenceID(ref)),\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createPluginNode(\n  id: number,\n  tag: string,\n  value: unknown,\n): SerovalPluginNode {\n  return createSerovalNode(\n    SerovalNodeType.Plugin,\n    id,\n    value,\n    NIL,\n    serializeString(tag),\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createArrayNode(\n  id: number,\n  current: unknown[],\n  parsedItems: SerovalNode[],\n): SerovalArrayNode {\n  return createSerovalNode(\n    SerovalNodeType.Array,\n    id,\n    NIL,\n    current.length,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    parsedItems,\n    NIL,\n    NIL,\n    getObjectFlag(current),\n  );\n}\n\nexport function createBoxedNode(\n  id: number,\n  boxed: SerovalNode,\n): SerovalBoxedNode {\n  return createSerovalNode(\n    SerovalNodeType.Boxed,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    boxed,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createTypedArrayNode(\n  id: number,\n  current: TypedArrayValue,\n  buffer: SerovalNode,\n): SerovalTypedArrayNode {\n  return createSerovalNode(\n    SerovalNodeType.TypedArray,\n    id,\n    NIL,\n    current.length,\n    current.constructor.name,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    buffer,\n    current.byteOffset,\n    NIL,\n  );\n}\n\nexport function createBigIntTypedArrayNode(\n  id: number,\n  current: BigIntTypedArrayValue,\n  buffer: SerovalNode,\n): SerovalBigIntTypedArrayNode {\n  return createSerovalNode(\n    SerovalNodeType.BigIntTypedArray,\n    id,\n    NIL,\n    current.length,\n    current.constructor.name,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    buffer,\n    current.byteOffset,\n    NIL,\n  );\n}\n\nexport function createDataViewNode(\n  id: number,\n  current: DataView,\n  buffer: SerovalNode,\n): SerovalDataViewNode {\n  return createSerovalNode(\n    SerovalNodeType.DataView,\n    id,\n    NIL,\n    current.byteLength,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    buffer,\n    current.byteOffset,\n    NIL,\n  );\n}\n\nexport function createErrorNode(\n  id: number,\n  current: Error,\n  options: SerovalObjectRecordNode | undefined,\n): SerovalErrorNode {\n  return createSerovalNode(\n    SerovalNodeType.Error,\n    id,\n    getErrorConstructor(current),\n    NIL,\n    NIL,\n    serializeString(current.message),\n    options,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createAggregateErrorNode(\n  id: number,\n  current: AggregateError,\n  options: SerovalObjectRecordNode | undefined,\n): SerovalAggregateErrorNode {\n  return createSerovalNode(\n    SerovalNodeType.AggregateError,\n    id,\n    getErrorConstructor(current),\n    NIL,\n    NIL,\n    serializeString(current.message),\n    options,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createSetNode(\n  id: number,\n  size: number,\n  items: SerovalNode[],\n): SerovalSetNode {\n  return createSerovalNode(\n    SerovalNodeType.Set,\n    id,\n    NIL,\n    size,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    items,\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createIteratorFactoryInstanceNode(\n  factory: SerovalNodeWithID,\n  items: SerovalNode,\n): SerovalIteratorFactoryInstanceNode {\n  return createSerovalNode(\n    SerovalNodeType.IteratorFactoryInstance,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    [factory, items],\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createAsyncIteratorFactoryInstanceNode(\n  factory: SerovalNodeWithID,\n  items: SerovalNode,\n): SerovalAsyncIteratorFactoryInstanceNode {\n  return createSerovalNode(\n    SerovalNodeType.AsyncIteratorFactoryInstance,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    [factory, items],\n    NIL,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createStreamConstructorNode(\n  id: number,\n  factory: SerovalNodeWithID,\n  sequence: SerovalNode[],\n): SerovalStreamConstructorNode {\n  return createSerovalNode(\n    SerovalNodeType.StreamConstructor,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    sequence,\n    factory,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createStreamNextNode(\n  id: number,\n  parsed: SerovalNode,\n): SerovalStreamNextNode {\n  return createSerovalNode(\n    SerovalNodeType.StreamNext,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    parsed,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createStreamThrowNode(\n  id: number,\n  parsed: SerovalNode,\n): SerovalStreamThrowNode {\n  return createSerovalNode(\n    SerovalNodeType.StreamThrow,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    parsed,\n    NIL,\n    NIL,\n  );\n}\n\nexport function createStreamReturnNode(\n  id: number,\n  parsed: SerovalNode,\n): SerovalStreamReturnNode {\n  return createSerovalNode(\n    SerovalNodeType.StreamReturn,\n    id,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    NIL,\n    parsed,\n    NIL,\n    NIL,\n  );\n}\n", "import { serializeString } from './string';\nimport type { SerovalNode } from './types';\n\nconst { toString: objectToString } = /* @__PURE__ */ Object.prototype;\n\nfunction getErrorMessage(type: string, cause: any): string {\n  if (cause instanceof Error) {\n    return `<PERSON><PERSON><PERSON> caught an error during the ${type} process.\n  \n${cause.name}\n${cause.message}\n\n- For more information, please check the \"cause\" property of this error.\n- If you believe this is an error in Seroval, please submit an issue at https://github.com/lxsmnsyc/seroval/issues/new`;\n  }\n  return `<PERSON><PERSON><PERSON> caught an error during the ${type} process.\n\n\"${objectToString.call(cause)}\"\n\nFor more information, please check the \"cause\" property of this error.`;\n}\n\nexport class SerovalError extends Error {\n  constructor(\n    type: string,\n    public cause: any,\n  ) {\n    super(getErrorMessage(type, cause));\n  }\n}\n\nexport class SerovalParserError extends SerovalError {\n  constructor(cause: any) {\n    super('parsing', cause);\n  }\n}\n\nexport class SerovalSerializationError extends SerovalError {\n  constructor(cause: any) {\n    super('serialization', cause);\n  }\n}\n\nexport class SerovalDeserializationError extends SerovalError {\n  constructor(cause: any) {\n    super('deserialization', cause);\n  }\n}\n\nexport class SerovalUnsupportedTypeError extends Error {\n  constructor(public value: unknown) {\n    super(\n      `The value ${objectToString.call(value)} of type \"${typeof value}\" cannot be parsed/serialized.\n      \nThere are few workarounds for this problem:\n- Transform the value in a way that it can be serialized.\n- If the reference is present on multiple runtimes (isomorphic), you can use the Reference API to map the references.`,\n    );\n  }\n}\n\nexport class SerovalUnsupportedNodeError extends Error {\n  constructor(node: SerovalNode) {\n    super('Unsupported node type \"' + node.t + '\".');\n  }\n}\n\nexport class SerovalMissingPluginError extends Error {\n  constructor(tag: string) {\n    super('Missing plugin for tag \"' + tag + '\".');\n  }\n}\n\nexport class SerovalMissingInstanceError extends Error {\n  constructor(tag: string) {\n    super('Missing \"' + tag + '\" instance.');\n  }\n}\n\nexport class SerovalMissingReferenceError extends Error {\n  constructor(public value: unknown) {\n    super(\n      'Missing reference for the value \"' +\n        objectToString.call(value) +\n        '\" of type \"' +\n        typeof value +\n        '\"',\n    );\n  }\n}\n\nexport class SerovalMissingReferenceForIdError extends Error {\n  constructor(id: string) {\n    super('Missing reference for id \"' + serializeString(id) + '\"');\n  }\n}\n\nexport class SerovalUnknownTypedArrayError extends Error {\n  constructor(name: string) {\n    super('Unknown TypedArray \"' + name + '\"');\n  }\n}\n", "/**\n * An opaque reference allows hiding values from the serializer.\n */\nexport class OpaqueReference<V, R = undefined> {\n  constructor(\n    public readonly value: V,\n    public readonly replacement?: R,\n  ) {}\n}\n", "import { Feature } from './compat';\n\nexport function createFunction(\n  features: number,\n  parameters: string[],\n  body: string,\n): string {\n  if (features & Feature.ArrowFunction) {\n    const joined =\n      parameters.length === 1\n        ? parameters[0]\n        : '(' + parameters.join(',') + ')';\n    return joined + '=>' + (body.startsWith('{') ? '(' + body + ')' : body);\n  }\n  return 'function(' + parameters.join(',') + '){return ' + body + '}';\n}\n\nexport function createEffectfulFunction(\n  features: number,\n  parameters: string[],\n  body: string,\n): string {\n  if (features & Feature.ArrowFunction) {\n    const joined =\n      parameters.length === 1\n        ? parameters[0]\n        : '(' + parameters.join(',') + ')';\n    return joined + '=>{' + body + '}';\n  }\n  return 'function(' + parameters.join(',') + '){' + body + '}';\n}\n", "import { createEffectfulFunction, createFunction } from './function-string';\n\nexport const ITERATOR = {};\n\nexport const ASYNC_ITERATOR = {};\n\nexport const enum SpecialReference {\n  MapSentinel = 0,\n  PromiseConstructor = 1,\n  PromiseSuccess = 2,\n  PromiseFailure = 3,\n  StreamConstructor = 4,\n}\n\n/**\n * Placeholder references\n */\nexport const SPECIAL_REFS: Record<SpecialReference, unknown> = {\n  [SpecialReference.MapSentinel]: {},\n  [SpecialReference.PromiseConstructor]: {},\n  [SpecialReference.PromiseSuccess]: {},\n  [SpecialReference.PromiseFailure]: {},\n  [SpecialReference.StreamConstructor]: {},\n};\n\nfunction serializePromiseConstructor(features: number): string {\n  return createFunction(\n    features,\n    ['r'],\n    '(r.p=new Promise(' +\n      createEffectfulFunction(features, ['s', 'f'], 'r.s=s,r.f=f') +\n      '))',\n  );\n}\n\nfunction serializePromiseSuccess(features: number): string {\n  return createEffectfulFunction(\n    features,\n    ['r', 'd'],\n    'r.s(d),r.p.s=1,r.p.v=d',\n  );\n}\n\nfunction serializePromiseFailure(features: number): string {\n  return createEffectfulFunction(\n    features,\n    ['r', 'd'],\n    'r.f(d),r.p.s=2,r.p.v=d',\n  );\n}\n\nfunction serializeStreamConstructor(features: number): string {\n  return createFunction(\n    features,\n    ['b', 'a', 's', 'l', 'p', 'f', 'e', 'n'],\n    '(b=[],a=!0,s=!1,l=[],p=0,f=' +\n      createEffectfulFunction(\n        features,\n        ['v', 'm', 'x'],\n        'for(x=0;x<p;x++)l[x]&&l[x][m](v)',\n      ) +\n      ',n=' +\n      createEffectfulFunction(\n        features,\n        ['o', 'x', 'z', 'c'],\n        'for(x=0,z=b.length;x<z;x++)(c=b[x],(!a&&x===z-1)?o[s?\"return\":\"throw\"](c):o.next(c))',\n      ) +\n      ',e=' +\n      createFunction(\n        features,\n        ['o', 't'],\n        '(a&&(l[t=p++]=o),n(o),' +\n          createEffectfulFunction(features, [], 'a&&(l[t]=void 0)') +\n          ')',\n      ) +\n      ',{__SEROVAL_STREAM__:!0,on:' +\n      createFunction(features, ['o'], 'e(o)') +\n      ',next:' +\n      createEffectfulFunction(features, ['v'], 'a&&(b.push(v),f(v,\"next\"))') +\n      ',throw:' +\n      createEffectfulFunction(\n        features,\n        ['v'],\n        'a&&(b.push(v),f(v,\"throw\"),a=s=!1,l.length=0)',\n      ) +\n      ',return:' +\n      createEffectfulFunction(\n        features,\n        ['v'],\n        'a&&(b.push(v),f(v,\"return\"),a=!1,s=!0,l.length=0)',\n      ) +\n      '})',\n  );\n}\n\nexport function serializeSpecialReferenceValue(\n  features: number,\n  ref: SpecialReference,\n): string {\n  switch (ref) {\n    case SpecialReference.MapSentinel:\n      return '[]';\n    case SpecialReference.PromiseConstructor:\n      return serializePromiseConstructor(features);\n    case SpecialReference.PromiseSuccess:\n      return serializePromiseSuccess(features);\n    case SpecialReference.PromiseFailure:\n      return serializePromiseFailure(features);\n    case SpecialReference.StreamConstructor:\n      return serializeStreamConstructor(features);\n    default:\n      return '';\n  }\n}\n", "export interface Deferred {\n  promise: Promise<unknown>;\n  resolve(value: unknown): void;\n  reject(value: unknown): void;\n}\n\nexport function createDeferred(): Deferred {\n  let resolve: Deferred['resolve'];\n  let reject: Deferred['reject'];\n  return {\n    promise: new Promise<unknown>((res, rej) => {\n      resolve = res;\n      reject = rej;\n    }),\n    resolve(value): void {\n      resolve(value);\n    },\n    reject(value): void {\n      reject(value);\n    },\n  };\n}\n", "import type { Deferred } from './utils/deferred';\nimport { createDeferred } from './utils/deferred';\n\ninterface StreamListener<T> {\n  next(value: T): void;\n  throw(value: unknown): void;\n  return(value: T): void;\n}\n\nexport interface Stream<T> {\n  __SEROVAL_STREAM__: true;\n\n  on(listener: StreamListener<T>): () => void;\n\n  next(value: T): void;\n  throw(value: unknown): void;\n  return(value: T): void;\n}\n\nexport function isStream<T>(value: object): value is Stream<T> {\n  return '__SEROVAL_STREAM__' in value;\n}\n\nexport function createStream<T>(): Stream<T> {\n  const listeners = new Set<StreamListener<T>>();\n  const buffer: unknown[] = [];\n  let alive = true;\n  let success = true;\n\n  function flushNext(value: T): void {\n    for (const listener of listeners.keys()) {\n      listener.next(value);\n    }\n  }\n\n  function flushThrow(value: unknown): void {\n    for (const listener of listeners.keys()) {\n      listener.throw(value);\n    }\n  }\n\n  function flushReturn(value: T): void {\n    for (const listener of listeners.keys()) {\n      listener.return(value);\n    }\n  }\n\n  return {\n    __SEROVAL_STREAM__: true,\n    on(listener: StreamListener<T>): () => void {\n      if (alive) {\n        listeners.add(listener);\n      }\n      for (let i = 0, len = buffer.length; i < len; i++) {\n        const value = buffer[i];\n        if (i === len - 1 && !alive) {\n          if (success) {\n            listener.return(value as T);\n          } else {\n            listener.throw(value);\n          }\n        } else {\n          listener.next(value as T);\n        }\n      }\n      return () => {\n        if (alive) {\n          listeners.delete(listener);\n        }\n      };\n    },\n    next(value): void {\n      if (alive) {\n        buffer.push(value);\n        flushNext(value);\n      }\n    },\n    throw(value): void {\n      if (alive) {\n        buffer.push(value);\n        flushThrow(value);\n        alive = false;\n        success = false;\n        listeners.clear();\n      }\n    },\n    return(value): void {\n      if (alive) {\n        buffer.push(value);\n        flushReturn(value);\n        alive = false;\n        success = true;\n        listeners.clear();\n      }\n    },\n  };\n}\n\nexport function createStreamFromAsyncIterable<T>(\n  iterable: AsyncIterable<T>,\n): Stream<T> {\n  const stream = createStream<T>();\n\n  const iterator = iterable[Symbol.asyncIterator]();\n\n  async function push(): Promise<void> {\n    try {\n      const value = await iterator.next();\n      if (value.done) {\n        stream.return(value.value as T);\n      } else {\n        stream.next(value.value);\n        await push();\n      }\n    } catch (error) {\n      stream.throw(error);\n    }\n  }\n\n  push().catch(() => {\n    // no-op\n  });\n\n  return stream;\n}\n\nexport function streamToAsyncIterable<T>(\n  stream: Stream<T>,\n): () => AsyncIterableIterator<T> {\n  return (): AsyncIterableIterator<T> => {\n    const buffer: T[] = [];\n    const pending: Deferred[] = [];\n    let count = 0;\n    let doneAt = -1;\n    let isThrow = false;\n\n    function resolveAll(): void {\n      for (let i = 0, len = pending.length; i < len; i++) {\n        pending[i].resolve({ done: true, value: undefined });\n      }\n    }\n\n    stream.on({\n      next(value) {\n        const current = pending.shift();\n        if (current) {\n          current.resolve({ done: false, value });\n        }\n        buffer.push(value);\n      },\n      throw(value) {\n        const current = pending.shift();\n        if (current) {\n          current.reject(value);\n        }\n        resolveAll();\n        doneAt = buffer.length;\n        buffer.push(value as T);\n        isThrow = true;\n      },\n      return(value) {\n        const current = pending.shift();\n        if (current) {\n          current.resolve({ done: true, value });\n        }\n        resolveAll();\n        doneAt = buffer.length;\n        buffer.push(value);\n      },\n    });\n\n    function finalize() {\n      const current = count++;\n      const value = buffer[current];\n      if (current !== doneAt) {\n        return { done: false, value };\n      }\n      if (isThrow) {\n        throw value;\n      }\n      return { done: true, value };\n    }\n\n    return {\n      [Symbol.asyncIterator](): AsyncIterableIterator<T> {\n        return this;\n      },\n      async next(): Promise<IteratorResult<T>> {\n        if (doneAt === -1) {\n          const current = count++;\n          if (current >= buffer.length) {\n            const deferred = createDeferred();\n            pending.push(deferred);\n            return (await deferred.promise) as Promise<IteratorResult<T>>;\n          }\n          return { done: false, value: buffer[current] };\n        }\n        if (count > doneAt) {\n          return { done: true, value: undefined };\n        }\n        return finalize();\n      },\n    };\n  };\n}\n", "import { NIL } from \"../constants\";\n\nexport interface Sequence {\n  v: unknown[];\n  t: number;\n  d: number;\n}\n\nexport function iteratorToSequence<T>(source: Iterable<T>): Sequence {\n  const values: unknown[] = [];\n  let throwsAt = -1;\n  let doneAt = -1;\n\n  const iterator = source[Symbol.iterator]();\n\n  while (true) {\n    try {\n      const value = iterator.next();\n      values.push(value.value);\n      if (value.done) {\n        doneAt = values.length - 1;\n        break;\n      }\n    } catch (error) {\n      throwsAt = values.length;\n      values.push(error);\n    }\n  }\n\n  return {\n    v: values,\n    t: throwsAt,\n    d: doneAt,\n  };\n}\n\nexport function sequenceToIterator<T>(\n  sequence: Sequence,\n): () => IterableIterator<T> {\n  return (): IterableIterator<T> => {\n    let index = 0;\n\n    return {\n      [Symbol.iterator](): IterableIterator<T> {\n        return this;\n      },\n      next(): IteratorResult<T> {\n        if (index > sequence.d) {\n          return {\n            done: true,\n            value: NIL,\n          };\n        }\n        const currentIndex = index++;\n        const currentItem = sequence.v[currentIndex];\n        if (currentIndex === sequence.t) {\n          throw currentItem;\n        }\n        return {\n          done: currentIndex === sequence.d,\n          value: currentItem as T,\n        };\n      },\n    };\n  };\n}\n", "export default async function promiseToResult(\n  current: Promise<unknown>,\n): Promise<[0 | 1, unknown]> {\n  try {\n    return [1, await current];\n  } catch (e) {\n    return [0, e];\n  }\n}\n", "import {\n  createIndexedValueNode,\n  createReferenceNode,\n  createWKSymbolNode,\n} from '../base-primitives';\nimport { ALL_ENABLED } from '../compat';\nimport type { WellKnownSymbols } from '../constants';\nimport { INV_SYMBOL_REF, NIL, SerovalNodeType } from '../constants';\nimport { SerovalUnsupportedTypeError } from '../errors';\nimport { createSerovalNode } from '../node';\nimport type { Plugin, PluginAccessOptions, SerovalMode } from '../plugin';\nimport { hasReferenceID } from '../reference';\nimport {\n  ASYNC_ITERATOR,\n  ITERATOR,\n  SPECIAL_REFS,\n  SpecialReference,\n} from '../special-reference';\nimport type {\n  SerovalAsyncIteratorFactoryNode,\n  SerovalIndexedValueNode,\n  SerovalIteratorFactoryNode,\n  SerovalMapNode,\n  SerovalNode,\n  SerovalNullConstructorNode,\n  SerovalObjectNode,\n  SerovalObjectRecordNode,\n  SerovalPromiseConstructorNode,\n  SerovalReferenceNode,\n  SerovalSpecialReferenceNode,\n  SerovalWKSymbolNode,\n} from '../types';\nimport assert from '../utils/assert';\nimport { getObjectFlag } from '../utils/get-object-flag';\n\nexport interface BaseParserContextOptions extends PluginAccessOptions {\n  disabledFeatures?: number;\n  refs?: Map<unknown, number>;\n}\n\nexport const enum ParserNodeType {\n  Fresh = 0,\n  Indexed = 1,\n  Referenced = 2,\n}\n\nexport interface FreshNode {\n  type: ParserNodeType.Fresh;\n  value: number;\n}\n\nexport interface IndexedNode {\n  type: ParserNodeType.Indexed;\n  value: SerovalIndexedValueNode;\n}\n\nexport interface ReferencedNode {\n  type: ParserNodeType.Referenced;\n  value: SerovalReferenceNode;\n}\n\ntype ObjectNode = FreshNode | IndexedNode | ReferencedNode;\n\nexport abstract class BaseParserContext implements PluginAccessOptions {\n  abstract readonly mode: SerovalMode;\n\n  features: number;\n\n  marked = new Set<number>();\n\n  refs: Map<unknown, number>;\n\n  plugins?: Plugin<any, any>[] | undefined;\n\n  constructor(options: BaseParserContextOptions) {\n    this.plugins = options.plugins;\n    this.features = ALL_ENABLED ^ (options.disabledFeatures || 0);\n    this.refs = options.refs || new Map<unknown, number>();\n  }\n\n  protected markRef(id: number): void {\n    this.marked.add(id);\n  }\n\n  protected isMarked(id: number): boolean {\n    return this.marked.has(id);\n  }\n\n  protected createIndex<T>(current: T): number {\n    const id = this.refs.size;\n    this.refs.set(current, id);\n    return id;\n  }\n\n  protected getIndexedValue<T>(current: T): FreshNode | IndexedNode {\n    const registeredId = this.refs.get(current);\n    if (registeredId != null) {\n      this.markRef(registeredId);\n      return {\n        type: ParserNodeType.Indexed,\n        value: createIndexedValueNode(registeredId),\n      };\n    }\n    return {\n      type: ParserNodeType.Fresh,\n      value: this.createIndex(current),\n    };\n  }\n\n  protected getReference<T>(current: T): ObjectNode {\n    const indexed = this.getIndexedValue(current);\n    if (indexed.type === ParserNodeType.Indexed) {\n      return indexed;\n    }\n    if (hasReferenceID(current)) {\n      return {\n        type: ParserNodeType.Referenced,\n        value: createReferenceNode(indexed.value, current),\n      };\n    }\n    return indexed;\n  }\n\n  protected parseWellKnownSymbol(\n    current: symbol,\n  ): SerovalIndexedValueNode | SerovalWKSymbolNode | SerovalReferenceNode {\n    const ref = this.getReference(current);\n    if (ref.type !== ParserNodeType.Fresh) {\n      return ref.value;\n    }\n    assert(current in INV_SYMBOL_REF, new SerovalUnsupportedTypeError(current));\n    return createWKSymbolNode(ref.value, current as WellKnownSymbols);\n  }\n\n  protected parseSpecialReference(\n    ref: SpecialReference,\n  ): SerovalIndexedValueNode | SerovalSpecialReferenceNode {\n    const result = this.getIndexedValue(SPECIAL_REFS[ref]);\n    if (result.type === ParserNodeType.Indexed) {\n      return result.value;\n    }\n    return createSerovalNode(\n      SerovalNodeType.SpecialReference,\n      result.value,\n      ref,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n    );\n  }\n\n  protected parseIteratorFactory():\n    | SerovalIndexedValueNode\n    | SerovalIteratorFactoryNode {\n    const result = this.getIndexedValue(ITERATOR);\n    if (result.type === ParserNodeType.Indexed) {\n      return result.value;\n    }\n    return createSerovalNode(\n      SerovalNodeType.IteratorFactory,\n      result.value,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      this.parseWellKnownSymbol(Symbol.iterator),\n      NIL,\n      NIL,\n    );\n  }\n\n  protected parseAsyncIteratorFactory():\n    | SerovalIndexedValueNode\n    | SerovalAsyncIteratorFactoryNode {\n    const result = this.getIndexedValue(ASYNC_ITERATOR);\n    if (result.type === ParserNodeType.Indexed) {\n      return result.value;\n    }\n    return createSerovalNode(\n      SerovalNodeType.AsyncIteratorFactory,\n      result.value,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      [\n        this.parseSpecialReference(SpecialReference.PromiseConstructor),\n        this.parseWellKnownSymbol(Symbol.asyncIterator),\n      ],\n      NIL,\n      NIL,\n      NIL,\n    );\n  }\n\n  protected createObjectNode(\n    id: number,\n    current: Record<string, unknown>,\n    empty: boolean,\n    record: SerovalObjectRecordNode,\n  ): SerovalObjectNode | SerovalNullConstructorNode {\n    return createSerovalNode(\n      empty ? SerovalNodeType.NullConstructor : SerovalNodeType.Object,\n      id,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      record,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      getObjectFlag(current),\n    );\n  }\n\n  protected createMapNode(\n    id: number,\n    k: SerovalNode[],\n    v: SerovalNode[],\n    s: number,\n  ): SerovalMapNode {\n    return createSerovalNode(\n      SerovalNodeType.Map,\n      id,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      { k, v, s },\n      NIL,\n      this.parseSpecialReference(SpecialReference.MapSentinel),\n      NIL,\n      NIL,\n    );\n  }\n\n  protected createPromiseConstructorNode(\n    id: number,\n    resolver: number,\n  ): SerovalPromiseConstructorNode {\n    return createSerovalNode(\n      SerovalNodeType.PromiseConstructor,\n      id,\n      resolver,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      this.parseSpecialReference(SpecialReference.PromiseConstructor),\n      NIL,\n      NIL,\n    );\n  }\n}\n", "import {\n  createAggregateErrorNode,\n  createArrayBuffer<PERSON><PERSON>,\n  createArrayNode,\n  createAsyncIteratorFactoryInstanceNode,\n  createBigIntNode,\n  createBigIntTypedArrayNode,\n  createBoxedNode,\n  createDataViewNode,\n  createDateNode,\n  createErrorNode,\n  createIteratorFactoryInstanceN<PERSON>,\n  createNumberNode,\n  createPluginNode,\n  createRegExpNode,\n  createSetNode,\n  createStreamConstructorNode,\n  createStreamNextNode,\n  createStreamReturnNode,\n  createStreamThrowNode,\n  createStringNode,\n  createTypedArrayNode,\n} from '../../base-primitives';\nimport { Feature } from '../../compat';\nimport { NIL, SerovalNodeType } from '../../constants';\nimport { SerovalParserError, SerovalUnsupportedTypeError } from '../../errors';\nimport {\n  FALSE_NODE,\n  NULL_NODE,\n  TRUE_NODE,\n  UNDEFINED_NODE,\n} from '../../literals';\nimport { createSerovalNode } from '../../node';\nimport { OpaqueReference } from '../../opaque-reference';\nimport { SpecialReference } from '../../special-reference';\nimport type { Stream } from '../../stream';\nimport { createStreamFromAsyncIterable, isStream } from '../../stream';\nimport { serializeString } from '../../string';\nimport type {\n  SerovalAggregateErrorNode,\n  SerovalArrayNode,\n  SerovalBigIntTypedArrayNode,\n  SerovalBoxedNode,\n  SerovalDataViewNode,\n  SerovalErrorNode,\n  SerovalMapNode,\n  SerovalNode,\n  SerovalNullConstructorNode,\n  SerovalObjectNode,\n  SerovalObjectRecordKey,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalPromiseNode,\n  SerovalSetNode,\n  SerovalStreamConstructorNode,\n  SerovalTypedArrayNode,\n} from '../../types';\nimport { getErrorOptions } from '../../utils/error';\nimport { iteratorToSequence } from '../../utils/iterator-to-sequence';\nimport promiseToResult from '../../utils/promise-to-result';\nimport type {\n  BigIntTypedArrayValue,\n  TypedArrayValue,\n} from '../../utils/typed-array';\nimport { BaseParserContext, ParserNodeType } from '../parser';\n\ntype ObjectLikeNode =\n  | SerovalObjectNode\n  | SerovalNullConstructorNode\n  | SerovalPromiseNode;\n\nexport default abstract class BaseAsyncParserContext extends BaseParserContext {\n  private async parseItems(current: unknown[]): Promise<SerovalNode[]> {\n    const nodes = [];\n    for (let i = 0, len = current.length; i < len; i++) {\n      // For consistency in holes\n      if (i in current) {\n        nodes[i] = await this.parse(current[i]);\n      }\n    }\n    return nodes;\n  }\n\n  private async parseArray(\n    id: number,\n    current: unknown[],\n  ): Promise<SerovalArrayNode> {\n    return createArrayNode(id, current, await this.parseItems(current));\n  }\n\n  private async parseProperties(\n    properties: Record<string | symbol, unknown>,\n  ): Promise<SerovalObjectRecordNode> {\n    const entries = Object.entries(properties);\n    const keyNodes: SerovalObjectRecordKey[] = [];\n    const valueNodes: SerovalNode[] = [];\n    for (let i = 0, len = entries.length; i < len; i++) {\n      keyNodes.push(serializeString(entries[i][0]));\n      valueNodes.push(await this.parse(entries[i][1]));\n    }\n    // Check special properties\n    let symbol = Symbol.iterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createIteratorFactoryInstanceNode(\n          this.parseIteratorFactory(),\n          await this.parse(\n            iteratorToSequence(properties as unknown as Iterable<unknown>),\n          ),\n        ),\n      );\n    }\n    symbol = Symbol.asyncIterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createAsyncIteratorFactoryInstanceNode(\n          this.parseAsyncIteratorFactory(),\n          await this.parse(\n            createStreamFromAsyncIterable(\n              properties as unknown as AsyncIterable<unknown>,\n            ),\n          ),\n        ),\n      );\n    }\n    symbol = Symbol.toStringTag;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(createStringNode(properties[symbol] as string));\n    }\n    symbol = Symbol.isConcatSpreadable;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(properties[symbol] ? TRUE_NODE : FALSE_NODE);\n    }\n    return {\n      k: keyNodes,\n      v: valueNodes,\n      s: keyNodes.length,\n    };\n  }\n\n  private async parsePlainObject(\n    id: number,\n    current: Record<string, unknown>,\n    empty: boolean,\n  ): Promise<ObjectLikeNode> {\n    return this.createObjectNode(\n      id,\n      current,\n      empty,\n      await this.parseProperties(current),\n    );\n  }\n\n  private async parseBoxed(\n    id: number,\n    current: object,\n  ): Promise<SerovalBoxedNode> {\n    return createBoxedNode(id, await this.parse(current.valueOf()));\n  }\n\n  private async parseTypedArray(\n    id: number,\n    current: TypedArrayValue,\n  ): Promise<SerovalTypedArrayNode> {\n    return createTypedArrayNode(id, current, await this.parse(current.buffer));\n  }\n\n  private async parseBigIntTypedArray(\n    id: number,\n    current: BigIntTypedArrayValue,\n  ): Promise<SerovalBigIntTypedArrayNode> {\n    return createBigIntTypedArrayNode(\n      id,\n      current,\n      await this.parse(current.buffer),\n    );\n  }\n\n  private async parseDataView(\n    id: number,\n    current: DataView,\n  ): Promise<SerovalDataViewNode> {\n    return createDataViewNode(id, current, await this.parse(current.buffer));\n  }\n\n  private async parseError(\n    id: number,\n    current: Error,\n  ): Promise<SerovalErrorNode> {\n    const options = getErrorOptions(current, this.features);\n    return createErrorNode(\n      id,\n      current,\n      options ? await this.parseProperties(options) : NIL,\n    );\n  }\n\n  private async parseAggregateError(\n    id: number,\n    current: AggregateError,\n  ): Promise<SerovalAggregateErrorNode> {\n    const options = getErrorOptions(current, this.features);\n    return createAggregateErrorNode(\n      id,\n      current,\n      options ? await this.parseProperties(options) : NIL,\n    );\n  }\n\n  private async parseMap(\n    id: number,\n    current: Map<unknown, unknown>,\n  ): Promise<SerovalMapNode> {\n    const keyNodes: SerovalNode[] = [];\n    const valueNodes: SerovalNode[] = [];\n    for (const [key, value] of current.entries()) {\n      keyNodes.push(await this.parse(key));\n      valueNodes.push(await this.parse(value));\n    }\n    return this.createMapNode(id, keyNodes, valueNodes, current.size);\n  }\n\n  private async parseSet(\n    id: number,\n    current: Set<unknown>,\n  ): Promise<SerovalSetNode> {\n    const items: SerovalNode[] = [];\n    for (const item of current.keys()) {\n      items.push(await this.parse(item));\n    }\n    return createSetNode(id, current.size, items);\n  }\n\n  private async parsePromise(\n    id: number,\n    current: Promise<unknown>,\n  ): Promise<SerovalPromiseNode> {\n    const [status, result] = await promiseToResult(current);\n\n    return createSerovalNode(\n      SerovalNodeType.Promise,\n      id,\n      status,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      NIL,\n      await this.parse(result),\n      NIL,\n      NIL,\n    );\n  }\n\n  private async parsePlugin(\n    id: number,\n    current: unknown,\n  ): Promise<SerovalPluginNode | undefined> {\n    const currentPlugins = this.plugins;\n    if (currentPlugins) {\n      for (let i = 0, len = currentPlugins.length; i < len; i++) {\n        const plugin = currentPlugins[i];\n        if (plugin.parse.async && plugin.test(current)) {\n          return createPluginNode(\n            id,\n            plugin.tag,\n            await plugin.parse.async(current, this, {\n              id,\n            }),\n          );\n        }\n      }\n    }\n    return NIL;\n  }\n\n  private async parseStream(\n    id: number,\n    current: Stream<unknown>,\n  ): Promise<SerovalStreamConstructorNode> {\n    return createStreamConstructorNode(\n      id,\n      this.parseSpecialReference(SpecialReference.StreamConstructor),\n      await new Promise<SerovalNode[]>((resolve, reject) => {\n        const sequence: SerovalNode[] = [];\n        const cleanup = current.on({\n          next: value => {\n            this.markRef(id);\n            this.parse(value).then(\n              data => {\n                sequence.push(createStreamNextNode(id, data));\n              },\n              data => {\n                reject(data);\n                cleanup();\n              },\n            );\n          },\n          throw: value => {\n            this.markRef(id);\n            this.parse(value).then(\n              data => {\n                sequence.push(createStreamThrowNode(id, data));\n                resolve(sequence);\n                cleanup();\n              },\n              data => {\n                reject(data);\n                cleanup();\n              },\n            );\n          },\n          return: value => {\n            this.markRef(id);\n            this.parse(value).then(\n              data => {\n                sequence.push(createStreamReturnNode(id, data));\n                resolve(sequence);\n                cleanup();\n              },\n              data => {\n                reject(data);\n                cleanup();\n              },\n            );\n          },\n        });\n      }),\n    );\n  }\n\n  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: ehh\n  private async parseObject(id: number, current: object): Promise<SerovalNode> {\n    if (Array.isArray(current)) {\n      return this.parseArray(id, current);\n    }\n    if (isStream(current)) {\n      return this.parseStream(id, current);\n    }\n    const currentClass = current.constructor;\n    if (currentClass === OpaqueReference) {\n      return this.parse(\n        (current as OpaqueReference<unknown, unknown>).replacement,\n      );\n    }\n    const parsed = await this.parsePlugin(id, current);\n    if (parsed) {\n      return parsed;\n    }\n    switch (currentClass) {\n      case Object:\n        return this.parsePlainObject(\n          id,\n          current as Record<string, unknown>,\n          false,\n        );\n      case NIL:\n        return this.parsePlainObject(\n          id,\n          current as Record<string, unknown>,\n          true,\n        );\n      case Date:\n        return createDateNode(id, current as unknown as Date);\n      case RegExp:\n        return createRegExpNode(id, current as unknown as RegExp);\n      case Error:\n      case EvalError:\n      case RangeError:\n      case ReferenceError:\n      case SyntaxError:\n      case TypeError:\n      case URIError:\n        return this.parseError(id, current as unknown as Error);\n      case Number:\n      case Boolean:\n      case String:\n      case BigInt:\n        return this.parseBoxed(id, current);\n      case ArrayBuffer:\n        return createArrayBufferNode(id, current as unknown as ArrayBuffer);\n      case Int8Array:\n      case Int16Array:\n      case Int32Array:\n      case Uint8Array:\n      case Uint16Array:\n      case Uint32Array:\n      case Uint8ClampedArray:\n      case Float32Array:\n      case Float64Array:\n        return this.parseTypedArray(id, current as unknown as TypedArrayValue);\n      case DataView:\n        return this.parseDataView(id, current as unknown as DataView);\n      case Map:\n        return this.parseMap(id, current as unknown as Map<unknown, unknown>);\n      case Set:\n        return this.parseSet(id, current as unknown as Set<unknown>);\n      default:\n        break;\n    }\n    // Promises\n    if (currentClass === Promise || current instanceof Promise) {\n      return this.parsePromise(id, current as unknown as Promise<unknown>);\n    }\n    const currentFeatures = this.features;\n    // BigInt Typed Arrays\n    if (currentFeatures & Feature.BigIntTypedArray) {\n      switch (currentClass) {\n        case BigInt64Array:\n        case BigUint64Array:\n          return this.parseBigIntTypedArray(\n            id,\n            current as unknown as BigIntTypedArrayValue,\n          );\n        default:\n          break;\n      }\n    }\n    if (\n      currentFeatures & Feature.AggregateError &&\n      typeof AggregateError !== 'undefined' &&\n      (currentClass === AggregateError || current instanceof AggregateError)\n    ) {\n      return this.parseAggregateError(id, current as unknown as AggregateError);\n    }\n    // Slow path. We only need to handle Errors and Iterators\n    // since they have very broad implementations.\n    if (current instanceof Error) {\n      return this.parseError(id, current);\n    }\n    // Generator functions don't have a global constructor\n    // despite existing\n    if (Symbol.iterator in current || Symbol.asyncIterator in current) {\n      return this.parsePlainObject(id, current, !!currentClass);\n    }\n    throw new SerovalUnsupportedTypeError(current);\n  }\n\n  protected async parseFunction(current: unknown): Promise<SerovalNode> {\n    const ref = this.getReference(current);\n    if (ref.type !== ParserNodeType.Fresh) {\n      return ref.value;\n    }\n    const plugin = await this.parsePlugin(ref.value, current);\n    if (plugin) {\n      return plugin;\n    }\n    throw new SerovalUnsupportedTypeError(current);\n  }\n\n  async parse<T>(current: T): Promise<SerovalNode> {\n    switch (typeof current) {\n      case 'boolean':\n        return current ? TRUE_NODE : FALSE_NODE;\n      case 'undefined':\n        return UNDEFINED_NODE;\n      case 'string':\n        return createStringNode(current as string);\n      case 'number':\n        return createNumberNode(current as number);\n      case 'bigint':\n        return createBigIntNode(current as bigint);\n      case 'object': {\n        if (current) {\n          const ref = this.getReference(current);\n          return ref.type === 0\n            ? await this.parseObject(ref.value, current as object)\n            : ref.value;\n        }\n        return NULL_NODE;\n      }\n      case 'symbol':\n        return this.parseWellKnownSymbol(current);\n      case 'function':\n        return this.parseFunction(current);\n      default:\n        throw new SerovalUnsupportedTypeError(current);\n    }\n  }\n\n  async parseTop<T>(current: T): Promise<SerovalNode> {\n    try {\n      return await this.parse(current);\n    } catch (error) {\n      throw error instanceof SerovalParserError\n        ? error\n        : new SerovalParserError(error);\n    }\n  }\n}\n", "import BaseAsyncParserContext from '../context/parser/async';\nimport type { SerovalMode } from '../plugin';\nimport type { CrossParserContextOptions } from './parser';\n\nexport type CrossAsyncParserContextOptions = CrossParserContextOptions;\n\nexport default class CrossAsyncParserContext extends BaseAsyncParserContext {\n  readonly mode: SerovalMode = 'cross';\n}\n", "import { SerovalUnknownTypedArrayError } from '../errors';\n\ntype TypedArrayConstructor =\n  | Int8ArrayConstructor\n  | Int16ArrayConstructor\n  | Int32ArrayConstructor\n  | Uint8ArrayConstructor\n  | Uint16ArrayConstructor\n  | Uint32ArrayConstructor\n  | Uint8ClampedArrayConstructor\n  | Float32ArrayConstructor\n  | Float64ArrayConstructor\n  | BigInt64ArrayConstructor\n  | BigUint64ArrayConstructor;\n\nexport type TypedArrayValue =\n  | Int8Array\n  | Int16Array\n  | Int32Array\n  | Uint8Array\n  | Uint16Array\n  | Uint32Array\n  | Uint8ClampedArray\n  | Float32Array\n  | Float64Array;\n\nexport type BigIntTypedArrayValue = BigInt64Array | BigUint64Array;\n\nexport function getTypedArrayConstructor(name: string): TypedArrayConstructor {\n  switch (name) {\n    case 'Int8Array':\n      return Int8Array;\n    case 'Int16Array':\n      return Int16Array;\n    case 'Int32Array':\n      return Int32Array;\n    case 'Uint8Array':\n      return Uint8Array;\n    case 'Uint16Array':\n      return Uint16Array;\n    case 'Uint32Array':\n      return Uint32Array;\n    case 'Uint8ClampedArray':\n      return Uint8ClampedArray;\n    case 'Float32Array':\n      return Float32Array;\n    case 'Float64Array':\n      return Float64Array;\n    case 'BigInt64Array':\n      return BigInt64Array;\n    case 'BigUint64Array':\n      return BigUint64Array;\n    default:\n      throw new SerovalUnknownTypedArrayError(name);\n  }\n}\n", "import {\n  CONSTANT_VAL,\n  ERROR_CONSTRUCTOR,\n  SYMBOL_REF,\n  SerovalNodeType,\n  SerovalObjectFlags,\n} from '../constants';\nimport {\n  SerovalDeserializationError,\n  SerovalMissingInstanceError,\n  SerovalMissingPluginError,\n  SerovalUnsupportedNodeError,\n} from '../errors';\nimport type { Plugin, PluginAccessOptions, SerovalMode } from '../plugin';\nimport { getReference } from '../reference';\nimport type { Stream } from '../stream';\nimport { createStream, streamToAsyncIterable } from '../stream';\nimport { deserializeString } from '../string';\nimport type {\n  SerovalAggregateErrorNode,\n  SerovalArrayBufferNode,\n  SerovalArrayNode,\n  SerovalAsyncIteratorFactoryInstanceNode,\n  SerovalAsyncIteratorFactoryNode,\n  SerovalBigIntTypedArrayNode,\n  SerovalBoxedNode,\n  SerovalDataViewNode,\n  SerovalDateNode,\n  SerovalErrorNode,\n  SerovalIteratorFactoryInstanceNode,\n  SerovalIteratorFactoryNode,\n  SerovalMapNode,\n  SerovalNode,\n  SerovalNullConstructorNode,\n  SerovalObjectNode,\n  SerovalObjectRecordKey,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalPromiseConstructorNode,\n  SerovalPromiseNode,\n  SerovalPromiseRejectNode,\n  SerovalPromiseResolveNode,\n  SerovalReferenceNode,\n  SerovalRegExpNode,\n  SerovalSetNode,\n  SerovalStreamConstructorNode,\n  SerovalStreamNextNode,\n  SerovalStreamReturnNode,\n  SerovalStreamThrowNode,\n  SerovalTypedArrayNode,\n} from '../types';\nimport assert from '../utils/assert';\nimport type { Deferred } from '../utils/deferred';\nimport { createDeferred } from '../utils/deferred';\nimport type { Sequence } from '../utils/iterator-to-sequence';\nimport { sequenceToIterator } from '../utils/iterator-to-sequence';\nimport type {\n  BigIntTypedArrayValue,\n  TypedArrayValue,\n} from '../utils/typed-array';\nimport { getTypedArrayConstructor } from '../utils/typed-array';\n\nfunction applyObjectFlag(obj: unknown, flag: SerovalObjectFlags): unknown {\n  switch (flag) {\n    case SerovalObjectFlags.Frozen:\n      return Object.freeze(obj);\n    case SerovalObjectFlags.NonExtensible:\n      return Object.preventExtensions(obj);\n    case SerovalObjectFlags.Sealed:\n      return Object.seal(obj);\n    default:\n      return obj;\n  }\n}\n\ntype AssignableValue = AggregateError | Error | Iterable<unknown>;\ntype AssignableNode = SerovalAggregateErrorNode | SerovalErrorNode;\n\nexport interface BaseDeserializerOptions extends PluginAccessOptions {\n  refs?: Map<number, unknown>;\n}\n\nexport default abstract class BaseDeserializerContext\n  implements PluginAccessOptions\n{\n  abstract readonly mode: SerovalMode;\n\n  /**\n   * Mapping ids to values\n   * @private\n   */\n  refs: Map<number, unknown>;\n\n  plugins?: Plugin<any, any>[] | undefined;\n\n  constructor(options: BaseDeserializerOptions) {\n    this.plugins = options.plugins;\n    this.refs = options.refs || new Map<number, unknown>();\n  }\n\n  protected abstract assignIndexedValue<T>(id: number, value: T): T;\n\n  private deserializeReference(node: SerovalReferenceNode): unknown {\n    return this.assignIndexedValue(\n      node.i,\n      getReference(deserializeString(node.s)),\n    );\n  }\n\n  private deserializeArray(node: SerovalArrayNode): unknown[] {\n    const len = node.l;\n    const result: unknown[] = this.assignIndexedValue(\n      node.i,\n      new Array<unknown>(len),\n    );\n    let item: SerovalNode | undefined;\n    for (let i = 0; i < len; i++) {\n      item = node.a[i];\n      if (item) {\n        result[i] = this.deserialize(item);\n      }\n    }\n    applyObjectFlag(result, node.o);\n    return result;\n  }\n\n  private deserializeProperties(\n    node: SerovalObjectRecordNode,\n    result: Record<string | symbol, unknown>,\n  ): Record<string | symbol, unknown> {\n    const len = node.s;\n    if (len) {\n      const keys = node.k;\n      const vals = node.v;\n      for (let i = 0, key: SerovalObjectRecordKey; i < len; i++) {\n        key = keys[i];\n        if (typeof key === 'string') {\n          result[deserializeString(key)] = this.deserialize(vals[i]);\n        } else {\n          result[this.deserialize(key) as symbol] = this.deserialize(vals[i]);\n        }\n      }\n    }\n    return result;\n  }\n\n  private deserializeObject(\n    node: SerovalObjectNode | SerovalNullConstructorNode,\n  ): Record<string, unknown> {\n    const result = this.assignIndexedValue(\n      node.i,\n      (node.t === SerovalNodeType.Object ? {} : Object.create(null)) as Record<\n        string,\n        unknown\n      >,\n    );\n    this.deserializeProperties(node.p, result);\n    applyObjectFlag(result, node.o);\n    return result;\n  }\n\n  private deserializeDate(node: SerovalDateNode): Date {\n    return this.assignIndexedValue(node.i, new Date(node.s));\n  }\n\n  private deserializeRegExp(node: SerovalRegExpNode): RegExp {\n    return this.assignIndexedValue(\n      node.i,\n      new RegExp(deserializeString(node.c), node.m),\n    );\n  }\n\n  private deserializeSet(node: SerovalSetNode): Set<unknown> {\n    const result = this.assignIndexedValue(node.i, new Set<unknown>());\n    const items = node.a;\n    for (let i = 0, len = node.l; i < len; i++) {\n      result.add(this.deserialize(items[i]));\n    }\n    return result;\n  }\n\n  private deserializeMap(node: SerovalMapNode): Map<unknown, unknown> {\n    const result = this.assignIndexedValue(node.i, new Map<unknown, unknown>());\n    const keys = node.e.k;\n    const vals = node.e.v;\n    for (let i = 0, len = node.e.s; i < len; i++) {\n      result.set(this.deserialize(keys[i]), this.deserialize(vals[i]));\n    }\n    return result;\n  }\n\n  private deserializeArrayBuffer(node: SerovalArrayBufferNode): ArrayBuffer {\n    const bytes = new Uint8Array(node.s);\n    const result = this.assignIndexedValue(node.i, bytes.buffer);\n    return result;\n  }\n\n  private deserializeTypedArray(\n    node: SerovalTypedArrayNode | SerovalBigIntTypedArrayNode,\n  ): TypedArrayValue | BigIntTypedArrayValue {\n    const construct = getTypedArrayConstructor(node.c) as Int8ArrayConstructor;\n    const source = this.deserialize(node.f) as ArrayBuffer;\n    const result = this.assignIndexedValue(\n      node.i,\n      new construct(source, node.b, node.l),\n    );\n    return result;\n  }\n\n  private deserializeDataView(node: SerovalDataViewNode): DataView {\n    const source = this.deserialize(node.f) as ArrayBuffer;\n    const result = this.assignIndexedValue(\n      node.i,\n      new DataView(source, node.b, node.l),\n    );\n    return result;\n  }\n\n  private deserializeDictionary<T extends AssignableValue>(\n    node: AssignableNode,\n    result: T,\n  ): T {\n    if (node.p) {\n      const fields = this.deserializeProperties(node.p, {});\n      Object.assign(result, fields);\n    }\n    return result;\n  }\n\n  private deserializeAggregateError(\n    node: SerovalAggregateErrorNode,\n  ): AggregateError {\n    // Serialize the required arguments\n    const result = this.assignIndexedValue(\n      node.i,\n      new AggregateError([], deserializeString(node.m)),\n    );\n    // `AggregateError` might've been extended\n    // either through class or custom properties\n    // Make sure to assign extra properties\n    return this.deserializeDictionary(node, result);\n  }\n\n  private deserializeError(node: SerovalErrorNode): Error {\n    const construct = ERROR_CONSTRUCTOR[node.s];\n    const result = this.assignIndexedValue(\n      node.i,\n      new construct(deserializeString(node.m)),\n    );\n    return this.deserializeDictionary(node, result);\n  }\n\n  private deserializePromise(node: SerovalPromiseNode): Promise<unknown> {\n    const deferred = createDeferred();\n    const result = this.assignIndexedValue(node.i, deferred);\n    const deserialized = this.deserialize(node.f);\n    if (node.s) {\n      deferred.resolve(deserialized);\n    } else {\n      deferred.reject(deserialized);\n    }\n    return result.promise;\n  }\n\n  private deserializeBoxed(node: SerovalBoxedNode): unknown {\n    return this.assignIndexedValue(node.i, Object(this.deserialize(node.f)));\n  }\n\n  private deserializePlugin(node: SerovalPluginNode): unknown {\n    const currentPlugins = this.plugins;\n    if (currentPlugins) {\n      const tag = deserializeString(node.c);\n      for (let i = 0, len = currentPlugins.length; i < len; i++) {\n        const plugin = currentPlugins[i];\n        if (plugin.tag === tag) {\n          return this.assignIndexedValue(\n            node.i,\n            plugin.deserialize(node.s, this, {\n              id: node.i,\n            }),\n          );\n        }\n      }\n    }\n    throw new SerovalMissingPluginError(node.c);\n  }\n\n  private deserializePromiseConstructor(\n    node: SerovalPromiseConstructorNode,\n  ): unknown {\n    return this.assignIndexedValue(\n      node.i,\n      this.assignIndexedValue(node.s, createDeferred()).promise,\n    );\n  }\n\n  private deserializePromiseResolve(node: SerovalPromiseResolveNode): unknown {\n    const deferred = this.refs.get(node.i) as Deferred | undefined;\n    assert(deferred, new SerovalMissingInstanceError('Promise'));\n    deferred.resolve(this.deserialize(node.a[1]));\n    return undefined;\n  }\n\n  private deserializePromiseReject(node: SerovalPromiseRejectNode): unknown {\n    const deferred = this.refs.get(node.i) as Deferred | undefined;\n    assert(deferred, new SerovalMissingInstanceError('Promise'));\n    deferred.reject(this.deserialize(node.a[1]));\n    return undefined;\n  }\n\n  private deserializeIteratorFactoryInstance(\n    node: SerovalIteratorFactoryInstanceNode,\n  ): unknown {\n    this.deserialize(node.a[0]);\n    const source = this.deserialize(node.a[1]);\n    return sequenceToIterator(source as Sequence);\n  }\n\n  private deserializeAsyncIteratorFactoryInstance(\n    node: SerovalAsyncIteratorFactoryInstanceNode,\n  ): unknown {\n    this.deserialize(node.a[0]);\n    const source = this.deserialize(node.a[1]);\n    return streamToAsyncIterable(source as Stream<any>);\n  }\n\n  private deserializeStreamConstructor(\n    node: SerovalStreamConstructorNode,\n  ): unknown {\n    const result = this.assignIndexedValue(node.i, createStream());\n    const len = node.a.length;\n    if (len) {\n      for (let i = 0; i < len; i++) {\n        this.deserialize(node.a[i]);\n      }\n    }\n    return result;\n  }\n\n  private deserializeStreamNext(node: SerovalStreamNextNode): unknown {\n    const deferred = this.refs.get(node.i) as Stream<unknown> | undefined;\n    assert(deferred, new SerovalMissingInstanceError('Stream'));\n    deferred.next(this.deserialize(node.f));\n    return undefined;\n  }\n\n  private deserializeStreamThrow(node: SerovalStreamThrowNode): unknown {\n    const deferred = this.refs.get(node.i) as Stream<unknown> | undefined;\n    assert(deferred, new SerovalMissingInstanceError('Stream'));\n    deferred.throw(this.deserialize(node.f));\n    return undefined;\n  }\n\n  private deserializeStreamReturn(node: SerovalStreamReturnNode): unknown {\n    const deferred = this.refs.get(node.i) as Stream<unknown> | undefined;\n    assert(deferred, new SerovalMissingInstanceError('Stream'));\n    deferred.return(this.deserialize(node.f));\n    return undefined;\n  }\n\n  private deserializeIteratorFactory(\n    node: SerovalIteratorFactoryNode,\n  ): unknown {\n    this.deserialize(node.f);\n    return undefined;\n  }\n\n  private deserializeAsyncIteratorFactory(\n    node: SerovalAsyncIteratorFactoryNode,\n  ): unknown {\n    this.deserialize(node.a[1]);\n    return undefined;\n  }\n\n  deserializeTop(node: SerovalNode): unknown {\n    try {\n      return this.deserialize(node);\n    } catch (error) {\n      throw new SerovalDeserializationError(error);\n    }\n  }\n\n  deserialize(node: SerovalNode): unknown {\n    switch (node.t) {\n      case SerovalNodeType.Constant:\n        return CONSTANT_VAL[node.s];\n      case SerovalNodeType.Number:\n        return node.s;\n      case SerovalNodeType.String:\n        return deserializeString(node.s);\n      case SerovalNodeType.BigInt:\n        return BigInt(node.s);\n      case SerovalNodeType.IndexedValue:\n        return this.refs.get(node.i);\n      case SerovalNodeType.Reference:\n        return this.deserializeReference(node);\n      case SerovalNodeType.Array:\n        return this.deserializeArray(node);\n      case SerovalNodeType.Object:\n      case SerovalNodeType.NullConstructor:\n        return this.deserializeObject(node);\n      case SerovalNodeType.Date:\n        return this.deserializeDate(node);\n      case SerovalNodeType.RegExp:\n        return this.deserializeRegExp(node);\n      case SerovalNodeType.Set:\n        return this.deserializeSet(node);\n      case SerovalNodeType.Map:\n        return this.deserializeMap(node);\n      case SerovalNodeType.ArrayBuffer:\n        return this.deserializeArrayBuffer(node);\n      case SerovalNodeType.BigIntTypedArray:\n      case SerovalNodeType.TypedArray:\n        return this.deserializeTypedArray(node);\n      case SerovalNodeType.DataView:\n        return this.deserializeDataView(node);\n      case SerovalNodeType.AggregateError:\n        return this.deserializeAggregateError(node);\n      case SerovalNodeType.Error:\n        return this.deserializeError(node);\n      case SerovalNodeType.Promise:\n        return this.deserializePromise(node);\n      case SerovalNodeType.WKSymbol:\n        return SYMBOL_REF[node.s];\n      case SerovalNodeType.Boxed:\n        return this.deserializeBoxed(node);\n      case SerovalNodeType.Plugin:\n        return this.deserializePlugin(node);\n      case SerovalNodeType.PromiseConstructor:\n        return this.deserializePromiseConstructor(node);\n      case SerovalNodeType.PromiseSuccess:\n        return this.deserializePromiseResolve(node);\n      case SerovalNodeType.PromiseFailure:\n        return this.deserializePromiseReject(node);\n      case SerovalNodeType.IteratorFactoryInstance:\n        return this.deserializeIteratorFactoryInstance(node);\n      case SerovalNodeType.AsyncIteratorFactoryInstance:\n        return this.deserializeAsyncIteratorFactoryInstance(node);\n      case SerovalNodeType.StreamConstructor:\n        return this.deserializeStreamConstructor(node);\n      case SerovalNodeType.StreamNext:\n        return this.deserializeStreamNext(node);\n      case SerovalNodeType.StreamThrow:\n        return this.deserializeStreamThrow(node);\n      case SerovalNodeType.StreamReturn:\n        return this.deserializeStreamReturn(node);\n      case SerovalNodeType.IteratorFactory:\n        return this.deserializeIteratorFactory(node);\n      case SerovalNodeType.AsyncIteratorFactory:\n        return this.deserializeAsyncIteratorFactory(node);\n      // case SerovalNodeType.SpecialReference:\n      default:\n        throw new SerovalUnsupportedNodeError(node);\n    }\n  }\n}\n", "import type { BaseDeserializerOptions } from '../context/deserializer';\nimport BaseDeserializerContext from '../context/deserializer';\nimport type { SerovalMode } from '../plugin';\n\nexport type CrossDeserializerContextOptions = BaseDeserializerOptions;\n\nexport default class CrossDeserializerContext extends BaseDeserializerContext {\n  readonly mode: SerovalMode = 'cross';\n\n  assignIndexedValue<T>(index: number, value: T): T {\n    if (!this.refs.has(index)) {\n      this.refs.set(index, value);\n    }\n    return value;\n  }\n}\n", "const IDENTIFIER_CHECK = /^[$A-Z_][0-9A-Z_$]*$/i;\n\nexport function isValidIdentifier(name: string): boolean {\n  const char = name[0];\n  return (\n    (char === '$' ||\n      char === '_' ||\n      (char >= 'A' && char <= 'Z') ||\n      (char >= 'a' && char <= 'z')) &&\n    IDENTIFIER_CHECK.test(name)\n  );\n}\n", "import { Feature } from '../compat';\nimport {\n  CONSTANT_STRING,\n  ERROR_CONSTRUCTOR_STRING,\n  NIL,\n  SYMBOL_STRING,\n  SerovalNodeType,\n  SerovalObjectFlags,\n} from '../constants';\nimport {\n  SerovalMissingPluginError,\n  SerovalSerializationError,\n  SerovalUnsupportedNodeError,\n} from '../errors';\nimport { createEffectfulFunction, createFunction } from '../function-string';\nimport { REFERENCES_KEY } from '../keys';\nimport type { Plugin, PluginAccessOptions, SerovalMode } from '../plugin';\nimport { serializeSpecialReferenceValue } from '../special-reference';\nimport type {\n  SerovalAggregateErrorNode,\n  SerovalArrayBufferNode,\n  SerovalArrayNode,\n  SerovalAsyncIteratorFactoryInstanceNode,\n  SerovalAsyncIteratorFactoryNode,\n  SerovalBigIntTypedArrayNode,\n  SerovalBoxedNode,\n  SerovalDataViewNode,\n  SerovalDateNode,\n  SerovalErrorNode,\n  SerovalIndexedValueNode,\n  SerovalIteratorFactoryInstanceNode,\n  SerovalIteratorFactoryNode,\n  SerovalMapNode,\n  SerovalNode,\n  SerovalNodeWithID,\n  SerovalNullConstructorNode,\n  SerovalObjectNode,\n  SerovalObjectRecordKey,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalPromiseConstructorNode,\n  SerovalPromiseNode,\n  SerovalPromiseRejectNode,\n  SerovalPromiseResolveNode,\n  SerovalReferenceNode,\n  SerovalRegExpNode,\n  SerovalSetNode,\n  SerovalSpecialReferenceNode,\n  SerovalStreamConstructorNode,\n  SerovalStreamNextNode,\n  SerovalStreamReturnNode,\n  SerovalStreamThrowNode,\n  SerovalTypedArrayNode,\n  SerovalWKSymbolNode,\n} from '../types';\nimport { isValidIdentifier } from '../utils/is-valid-identifier';\n\nconst enum AssignmentType {\n  Index = 0,\n  Add = 1,\n  Set = 2,\n  Delete = 3,\n}\n\ninterface IndexAssignment {\n  t: AssignmentType.Index;\n  s: string;\n  k: undefined;\n  v: string;\n}\n\ninterface SetAssignment {\n  t: AssignmentType.Set;\n  s: string;\n  k: string;\n  v: string;\n}\n\ninterface AddAssignment {\n  t: AssignmentType.Add;\n  s: string;\n  k: undefined;\n  v: string;\n}\n\ninterface DeleteAssignment {\n  t: AssignmentType.Delete;\n  s: string;\n  k: string;\n  v: undefined;\n}\n\n// Array of assignments to be done (used for recursion)\ntype Assignment =\n  | IndexAssignment\n  | AddAssignment\n  | SetAssignment\n  | DeleteAssignment;\n\nexport interface FlaggedObject {\n  type: SerovalObjectFlags;\n  value: string;\n}\n\nfunction getAssignmentExpression(assignment: Assignment): string {\n  switch (assignment.t) {\n    case AssignmentType.Index:\n      return assignment.s + '=' + assignment.v;\n    case AssignmentType.Set:\n      return assignment.s + '.set(' + assignment.k + ',' + assignment.v + ')';\n    case AssignmentType.Add:\n      return assignment.s + '.add(' + assignment.v + ')';\n    case AssignmentType.Delete:\n      return assignment.s + '.delete(' + assignment.k + ')';\n  }\n}\n\nfunction mergeAssignments(assignments: Assignment[]): Assignment[] {\n  const newAssignments: Assignment[] = [];\n  let current = assignments[0];\n  for (\n    let i = 1, len = assignments.length, item: Assignment, prev = current;\n    i < len;\n    i++\n  ) {\n    item = assignments[i];\n    if (item.t === AssignmentType.Index && item.v === prev.v) {\n      // Merge if the right-hand value is the same\n      // saves at least 2 chars\n      current = {\n        t: AssignmentType.Index,\n        s: item.s,\n        k: NIL,\n        v: getAssignmentExpression(current),\n      } as IndexAssignment;\n    } else if (item.t === AssignmentType.Set && item.s === prev.s) {\n      // Maps has chaining methods, merge if source is the same\n      current = {\n        t: AssignmentType.Set,\n        s: getAssignmentExpression(current),\n        k: item.k,\n        v: item.v,\n      } as SetAssignment;\n    } else if (item.t === AssignmentType.Add && item.s === prev.s) {\n      // Sets has chaining methods too\n      current = {\n        t: AssignmentType.Add,\n        s: getAssignmentExpression(current),\n        k: NIL,\n        v: item.v,\n      } as AddAssignment;\n    } else if (item.t === AssignmentType.Delete && item.s === prev.s) {\n      // Maps has chaining methods, merge if source is the same\n      current = {\n        t: AssignmentType.Delete,\n        s: getAssignmentExpression(current),\n        k: item.k,\n        v: NIL,\n      } as DeleteAssignment;\n    } else {\n      // Different assignment, push current\n      newAssignments.push(current);\n      current = item;\n    }\n    prev = item;\n  }\n\n  newAssignments.push(current);\n\n  return newAssignments;\n}\n\nfunction resolveAssignments(assignments: Assignment[]): string | undefined {\n  if (assignments.length) {\n    let result = '';\n    const merged = mergeAssignments(assignments);\n    for (let i = 0, len = merged.length; i < len; i++) {\n      result += getAssignmentExpression(merged[i]) + ',';\n    }\n    return result;\n  }\n  return NIL;\n}\n\nconst NULL_CONSTRUCTOR = 'Object.create(null)';\nconst SET_CONSTRUCTOR = 'new Set';\nconst MAP_CONSTRUCTOR = 'new Map';\n\nconst PROMISE_RESOLVE = 'Promise.resolve';\nconst PROMISE_REJECT = 'Promise.reject';\n\nconst OBJECT_FLAG_CONSTRUCTOR: Record<SerovalObjectFlags, string | undefined> =\n  {\n    [SerovalObjectFlags.Frozen]: 'Object.freeze',\n    [SerovalObjectFlags.Sealed]: 'Object.seal',\n    [SerovalObjectFlags.NonExtensible]: 'Object.preventExtensions',\n    [SerovalObjectFlags.None]: NIL,\n  };\n\ntype SerovalNodeWithProperties =\n  | SerovalObjectNode\n  | SerovalNullConstructorNode\n  | SerovalAggregateErrorNode\n  | SerovalErrorNode;\n\nexport interface BaseSerializerContextOptions extends PluginAccessOptions {\n  features: number;\n  markedRefs: number[] | Set<number>;\n}\n\nexport default abstract class BaseSerializerContext\n  implements PluginAccessOptions\n{\n  /**\n   * @private\n   */\n  features: number;\n\n  /**\n   * To check if an object is synchronously referencing itself\n   * @private\n   */\n  stack: number[] = [];\n\n  /**\n   * Array of object mutations\n   * @private\n   */\n  flags: FlaggedObject[] = [];\n\n  /**\n   * Array of assignments to be done (used for recursion)\n   * @private\n   */\n  assignments: Assignment[] = [];\n\n  plugins?: Plugin<any, any>[] | undefined;\n\n  /**\n   * Refs that are...referenced\n   * @private\n   */\n  marked: Set<number>;\n\n  constructor(options: BaseSerializerContextOptions) {\n    this.plugins = options.plugins;\n    this.features = options.features;\n    this.marked = new Set(options.markedRefs);\n  }\n\n  abstract readonly mode: SerovalMode;\n\n  createFunction(parameters: string[], body: string): string {\n    return createFunction(this.features, parameters, body);\n  }\n\n  createEffectfulFunction(parameters: string[], body: string): string {\n    return createEffectfulFunction(this.features, parameters, body);\n  }\n\n  /**\n   * A tiny function that tells if a reference\n   * is to be accessed. This is a requirement for\n   * deciding whether or not we should generate\n   * an identifier for the object\n   */\n  protected markRef(id: number): void {\n    this.marked.add(id);\n  }\n\n  protected isMarked(id: number): boolean {\n    return this.marked.has(id);\n  }\n\n  /**\n   * Converts the ID of a reference into a identifier string\n   * that is used to refer to the object instance in the\n   * generated script.\n   */\n  abstract getRefParam(id: number): string;\n\n  protected pushObjectFlag(flag: SerovalObjectFlags, id: number): void {\n    if (flag !== SerovalObjectFlags.None) {\n      this.markRef(id);\n      this.flags.push({\n        type: flag,\n        value: this.getRefParam(id),\n      });\n    }\n  }\n\n  private resolveFlags(): string | undefined {\n    let result = '';\n    for (let i = 0, current = this.flags, len = current.length; i < len; i++) {\n      const flag = current[i];\n      result += OBJECT_FLAG_CONSTRUCTOR[flag.type] + '(' + flag.value + '),';\n    }\n    return result;\n  }\n\n  protected resolvePatches(): string | undefined {\n    const assignments = resolveAssignments(this.assignments);\n    const flags = this.resolveFlags();\n    if (assignments) {\n      if (flags) {\n        return assignments + flags;\n      }\n      return assignments;\n    }\n    return flags;\n  }\n\n  /**\n   * Generates the inlined assignment for the reference\n   * This is different from the assignments array as this one\n   * signifies creation rather than mutation\n   */\n  protected createAssignment(source: string, value: string): void {\n    this.assignments.push({\n      t: AssignmentType.Index,\n      s: source,\n      k: NIL,\n      v: value,\n    });\n  }\n\n  protected createAddAssignment(ref: number, value: string): void {\n    this.assignments.push({\n      t: AssignmentType.Add,\n      s: this.getRefParam(ref),\n      k: NIL,\n      v: value,\n    });\n  }\n\n  protected createSetAssignment(ref: number, key: string, value: string): void {\n    this.assignments.push({\n      t: AssignmentType.Set,\n      s: this.getRefParam(ref),\n      k: key,\n      v: value,\n    });\n  }\n\n  protected createDeleteAssignment(ref: number, key: string): void {\n    this.assignments.push({\n      t: AssignmentType.Delete,\n      s: this.getRefParam(ref),\n      k: key,\n      v: NIL,\n    });\n  }\n\n  protected createArrayAssign(\n    ref: number,\n    index: number | string,\n    value: string,\n  ): void {\n    this.createAssignment(this.getRefParam(ref) + '[' + index + ']', value);\n  }\n\n  protected createObjectAssign(ref: number, key: string, value: string): void {\n    this.createAssignment(this.getRefParam(ref) + '.' + key, value);\n  }\n\n  /**\n   * Checks if the value is in the stack. Stack here is a reference\n   * structure to know if a object is to be accessed in a TDZ.\n   */\n  isIndexedValueInStack(node: SerovalNode): boolean {\n    return (\n      node.t === SerovalNodeType.IndexedValue && this.stack.includes(node.i)\n    );\n  }\n\n  /**\n   * Produces an assignment expression. `id` generates a reference\n   * parameter (through `getRefParam`) and has the option to\n   * return the reference parameter directly or assign a value to\n   * it.\n   */\n  protected abstract assignIndexedValue(id: number, value: string): string;\n\n  protected serializeReference(node: SerovalReferenceNode): string {\n    return this.assignIndexedValue(\n      node.i,\n      REFERENCES_KEY + '.get(\"' + node.s + '\")',\n    );\n  }\n\n  protected serializeArrayItem(\n    id: number,\n    item: SerovalNode | undefined,\n    index: number,\n  ): string {\n    // Check if index is a hole\n    if (item) {\n      // Check if item is a parent\n      if (this.isIndexedValueInStack(item)) {\n        this.markRef(id);\n        this.createArrayAssign(\n          id,\n          index,\n          this.getRefParam((item as SerovalIndexedValueNode).i),\n        );\n        return '';\n      }\n      return this.serialize(item);\n    }\n    return '';\n  }\n\n  protected serializeArray(node: SerovalArrayNode): string {\n    const id = node.i;\n    if (node.l) {\n      this.stack.push(id);\n      const list = node.a;\n      let values = this.serializeArrayItem(id, list[0], 0);\n      // This is different than Map and Set\n      // because we also need to serialize\n      // the holes of the Array\n      let isHoley = values === '';\n      for (let i = 1, len = node.l, item: string; i < len; i++) {\n        item = this.serializeArrayItem(id, list[i], i);\n        values += ',' + item;\n        isHoley = item === '';\n      }\n      this.stack.pop();\n      this.pushObjectFlag(node.o, node.i);\n      return this.assignIndexedValue(id, '[' + values + (isHoley ? ',]' : ']'));\n    }\n    return this.assignIndexedValue(id, '[]');\n  }\n\n  protected serializeProperty(\n    source: SerovalNodeWithProperties,\n    key: SerovalObjectRecordKey,\n    val: SerovalNode,\n  ): string {\n    if (typeof key === 'string') {\n      const check = Number(key);\n      const isIdentifier =\n        // Test if key is a valid positive number or JS identifier\n        // so that we don't have to serialize the key and wrap with brackets\n        (check >= 0 &&\n          // It's also important to consider that if the key is\n          // indeed numeric, we need to make sure that when\n          // converted back into a string, it's still the same\n          // to the original key. This allows us to differentiate\n          // keys that has numeric formats but in a different\n          // format, which can cause unintentional key declaration\n          // Example: { 0x1: 1 } vs { '0x1': 1 }\n          check.toString() === key) ||\n        isValidIdentifier(key);\n      if (this.isIndexedValueInStack(val)) {\n        const refParam = this.getRefParam((val as SerovalIndexedValueNode).i);\n        this.markRef(source.i);\n        // Strict identifier check, make sure\n        // that it isn't numeric (except NaN)\n        if (isIdentifier && check !== check) {\n          this.createObjectAssign(source.i, key, refParam);\n        } else {\n          this.createArrayAssign(\n            source.i,\n            isIdentifier ? key : '\"' + key + '\"',\n            refParam,\n          );\n        }\n        return '';\n      }\n      return (isIdentifier ? key : '\"' + key + '\"') + ':' + this.serialize(val);\n    }\n    return '[' + this.serialize(key) + ']:' + this.serialize(val);\n  }\n\n  protected serializeProperties(\n    source: SerovalNodeWithProperties,\n    record: SerovalObjectRecordNode,\n  ): string {\n    const len = record.s;\n    if (len) {\n      const keys = record.k;\n      const values = record.v;\n      this.stack.push(source.i);\n      let result = this.serializeProperty(source, keys[0], values[0]);\n      for (let i = 1, item = result; i < len; i++) {\n        item = this.serializeProperty(source, keys[i], values[i]);\n        result += (item && result && ',') + item;\n      }\n      this.stack.pop();\n      return '{' + result + '}';\n    }\n    return '{}';\n  }\n\n  protected serializeObject(node: SerovalObjectNode): string {\n    this.pushObjectFlag(node.o, node.i);\n    return this.assignIndexedValue(\n      node.i,\n      this.serializeProperties(node, node.p),\n    );\n  }\n\n  protected serializeWithObjectAssign(\n    source: SerovalNodeWithProperties,\n    value: SerovalObjectRecordNode,\n    serialized: string,\n  ): string {\n    const fields = this.serializeProperties(source, value);\n    if (fields !== '{}') {\n      return 'Object.assign(' + serialized + ',' + fields + ')';\n    }\n    return serialized;\n  }\n\n  private serializeStringKeyAssignment(\n    source: SerovalNodeWithProperties,\n    mainAssignments: Assignment[],\n    key: string,\n    value: SerovalNode,\n  ): void {\n    const serialized = this.serialize(value);\n    const check = Number(key);\n    const isIdentifier =\n      // Test if key is a valid positive number or JS identifier\n      // so that we don't have to serialize the key and wrap with brackets\n      (check >= 0 &&\n        // It's also important to consider that if the key is\n        // indeed numeric, we need to make sure that when\n        // converted back into a string, it's still the same\n        // to the original key. This allows us to differentiate\n        // keys that has numeric formats but in a different\n        // format, which can cause unintentional key declaration\n        // Example: { 0x1: 1 } vs { '0x1': 1 }\n        check.toString() === key) ||\n      isValidIdentifier(key);\n    if (this.isIndexedValueInStack(value)) {\n      // Strict identifier check, make sure\n      // that it isn't numeric (except NaN)\n      if (isIdentifier && check !== check) {\n        this.createObjectAssign(source.i, key, serialized);\n      } else {\n        this.createArrayAssign(\n          source.i,\n          isIdentifier ? key : '\"' + key + '\"',\n          serialized,\n        );\n      }\n    } else {\n      const parentAssignment = this.assignments;\n      this.assignments = mainAssignments;\n      if (isIdentifier && check !== check) {\n        this.createObjectAssign(source.i, key, serialized);\n      } else {\n        this.createArrayAssign(\n          source.i,\n          isIdentifier ? key : '\"' + key + '\"',\n          serialized,\n        );\n      }\n      this.assignments = parentAssignment;\n    }\n  }\n\n  protected serializeAssignment(\n    source: SerovalNodeWithProperties,\n    mainAssignments: Assignment[],\n    key: SerovalObjectRecordKey,\n    value: SerovalNode,\n  ): void {\n    if (typeof key === 'string') {\n      this.serializeStringKeyAssignment(source, mainAssignments, key, value);\n    } else {\n      const parent = this.stack;\n      this.stack = [];\n      const serialized = this.serialize(value);\n      this.stack = parent;\n      const parentAssignment = this.assignments;\n      this.assignments = mainAssignments;\n      this.createArrayAssign(source.i, this.serialize(key), serialized);\n      this.assignments = parentAssignment;\n    }\n  }\n\n  protected serializeAssignments(\n    source: SerovalNodeWithProperties,\n    node: SerovalObjectRecordNode,\n  ): string | undefined {\n    const len = node.s;\n    if (len) {\n      const mainAssignments: Assignment[] = [];\n      const keys = node.k;\n      const values = node.v;\n      this.stack.push(source.i);\n      for (let i = 0; i < len; i++) {\n        this.serializeAssignment(source, mainAssignments, keys[i], values[i]);\n      }\n      this.stack.pop();\n      return resolveAssignments(mainAssignments);\n    }\n    return NIL;\n  }\n\n  protected serializeDictionary(\n    node: SerovalNodeWithProperties,\n    init: string,\n  ): string {\n    if (node.p) {\n      if (this.features & Feature.ObjectAssign) {\n        init = this.serializeWithObjectAssign(node, node.p, init);\n      } else {\n        this.markRef(node.i);\n        const assignments = this.serializeAssignments(node, node.p);\n        if (assignments) {\n          return (\n            '(' +\n            this.assignIndexedValue(node.i, init) +\n            ',' +\n            assignments +\n            this.getRefParam(node.i) +\n            ')'\n          );\n        }\n      }\n    }\n    return this.assignIndexedValue(node.i, init);\n  }\n\n  protected serializeNullConstructor(node: SerovalNullConstructorNode): string {\n    this.pushObjectFlag(node.o, node.i);\n    return this.serializeDictionary(node, NULL_CONSTRUCTOR);\n  }\n\n  protected serializeDate(node: SerovalDateNode): string {\n    return this.assignIndexedValue(node.i, 'new Date(\"' + node.s + '\")');\n  }\n\n  protected serializeRegExp(node: SerovalRegExpNode): string {\n    return this.assignIndexedValue(node.i, '/' + node.c + '/' + node.m);\n  }\n\n  protected serializeSetItem(id: number, item: SerovalNode): string {\n    if (this.isIndexedValueInStack(item)) {\n      this.markRef(id);\n      this.createAddAssignment(\n        id,\n        this.getRefParam((item as SerovalIndexedValueNode).i),\n      );\n      return '';\n    }\n    return this.serialize(item);\n  }\n\n  protected serializeSet(node: SerovalSetNode): string {\n    let serialized = SET_CONSTRUCTOR;\n    const size = node.l;\n    const id = node.i;\n    if (size) {\n      const items = node.a;\n      this.stack.push(id);\n      let result = this.serializeSetItem(id, items[0]);\n      for (let i = 1, item = result; i < size; i++) {\n        item = this.serializeSetItem(id, items[i]);\n        result += (item && result && ',') + item;\n      }\n      this.stack.pop();\n      if (result) {\n        serialized += '([' + result + '])';\n      }\n    }\n    return this.assignIndexedValue(id, serialized);\n  }\n\n  protected serializeMapEntry(\n    id: number,\n    key: SerovalNode,\n    val: SerovalNode,\n    sentinel: string,\n  ): string {\n    if (this.isIndexedValueInStack(key)) {\n      // Create reference for the map instance\n      const keyRef = this.getRefParam((key as SerovalIndexedValueNode).i);\n      this.markRef(id);\n      // Check if value is a parent\n      if (this.isIndexedValueInStack(val)) {\n        const valueRef = this.getRefParam((val as SerovalIndexedValueNode).i);\n        // Register an assignment since\n        // both key and value are a parent of this\n        // Map instance\n        this.createSetAssignment(id, keyRef, valueRef);\n        return '';\n      }\n      // Reset the stack\n      // This is required because the serialized\n      // value is no longer part of the expression\n      // tree and has been moved to the deferred\n      // assignment\n      if (\n        val.t !== SerovalNodeType.IndexedValue &&\n        val.i != null &&\n        this.isMarked(val.i)\n      ) {\n        // We use a trick here using sequence (or comma) expressions\n        // basically we serialize the intended object in place WITHOUT\n        // actually returning it, this is by returning a placeholder\n        // value that we will remove sometime after.\n        const serialized =\n          '(' + this.serialize(val) + ',[' + sentinel + ',' + sentinel + '])';\n        this.createSetAssignment(id, keyRef, this.getRefParam(val.i));\n        this.createDeleteAssignment(id, sentinel);\n        return serialized;\n      }\n      const parent = this.stack;\n      this.stack = [];\n      this.createSetAssignment(id, keyRef, this.serialize(val));\n      this.stack = parent;\n      return '';\n    }\n    if (this.isIndexedValueInStack(val)) {\n      // Create ref for the Map instance\n      const valueRef = this.getRefParam((val as SerovalIndexedValueNode).i);\n      this.markRef(id);\n      if (\n        key.t !== SerovalNodeType.IndexedValue &&\n        key.i != null &&\n        this.isMarked(key.i)\n      ) {\n        const serialized =\n          '(' + this.serialize(key) + ',[' + sentinel + ',' + sentinel + '])';\n        this.createSetAssignment(id, this.getRefParam(key.i), valueRef);\n        this.createDeleteAssignment(id, sentinel);\n        return serialized;\n      }\n      // Reset stack for the key serialization\n      const parent = this.stack;\n      this.stack = [];\n      this.createSetAssignment(id, this.serialize(key), valueRef);\n      this.stack = parent;\n      return '';\n    }\n\n    return '[' + this.serialize(key) + ',' + this.serialize(val) + ']';\n  }\n\n  protected serializeMap(node: SerovalMapNode): string {\n    let serialized = MAP_CONSTRUCTOR;\n    const size = node.e.s;\n    const id = node.i;\n    const sentinel = node.f;\n    const sentinelId = this.getRefParam(sentinel.i);\n    if (size) {\n      const keys = node.e.k;\n      const vals = node.e.v;\n      this.stack.push(id);\n      let result = this.serializeMapEntry(id, keys[0], vals[0], sentinelId);\n      for (let i = 1, item = result; i < size; i++) {\n        item = this.serializeMapEntry(id, keys[i], vals[i], sentinelId);\n        result += (item && result && ',') + item;\n      }\n      this.stack.pop();\n      // Check if there are any values\n      // so that the empty Map constructor\n      // can be used instead\n      if (result) {\n        serialized += '([' + result + '])';\n      }\n    }\n    if (sentinel.t === SerovalNodeType.SpecialReference) {\n      this.markRef(sentinel.i);\n      serialized = '(' + this.serialize(sentinel) + ',' + serialized + ')';\n    }\n    return this.assignIndexedValue(id, serialized);\n  }\n\n  protected serializeArrayBuffer(node: SerovalArrayBufferNode): string {\n    let result = 'new Uint8Array(';\n    const buffer = node.s;\n    const len = buffer.length;\n    if (len) {\n      result += '[' + buffer[0];\n      for (let i = 1; i < len; i++) {\n        result += ',' + buffer[i];\n      }\n      result += ']';\n    }\n    return this.assignIndexedValue(node.i, result + ').buffer');\n  }\n\n  protected serializeTypedArray(\n    node: SerovalTypedArrayNode | SerovalBigIntTypedArrayNode,\n  ): string {\n    return this.assignIndexedValue(\n      node.i,\n      'new ' +\n        node.c +\n        '(' +\n        this.serialize(node.f) +\n        ',' +\n        node.b +\n        ',' +\n        node.l +\n        ')',\n    );\n  }\n\n  protected serializeDataView(node: SerovalDataViewNode): string {\n    return this.assignIndexedValue(\n      node.i,\n      'new DataView(' +\n        this.serialize(node.f) +\n        ',' +\n        node.b +\n        ',' +\n        node.l +\n        ')',\n    );\n  }\n\n  protected serializeAggregateError(node: SerovalAggregateErrorNode): string {\n    const id = node.i;\n    // `AggregateError` might've been extended\n    // either through class or custom properties\n    // Make sure to assign extra properties\n    this.stack.push(id);\n    const serialized = this.serializeDictionary(\n      node,\n      'new AggregateError([],\"' + node.m + '\")',\n    );\n    this.stack.pop();\n    return serialized;\n  }\n\n  protected serializeError(node: SerovalErrorNode): string {\n    return this.serializeDictionary(\n      node,\n      'new ' + ERROR_CONSTRUCTOR_STRING[node.s] + '(\"' + node.m + '\")',\n    );\n  }\n\n  protected serializePromise(node: SerovalPromiseNode): string {\n    let serialized: string;\n    // Check if resolved value is a parent expression\n    const fulfilled = node.f;\n    const id = node.i;\n    const promiseConstructor = node.s ? PROMISE_RESOLVE : PROMISE_REJECT;\n    if (this.isIndexedValueInStack(fulfilled)) {\n      // A Promise trick, reference the value\n      // inside the `then` expression so that\n      // the Promise evaluates after the parent\n      // has initialized\n      const ref = this.getRefParam((fulfilled as SerovalIndexedValueNode).i);\n      serialized =\n        promiseConstructor +\n        (node.s\n          ? '().then(' + this.createFunction([], ref) + ')'\n          : '().catch(' +\n            this.createEffectfulFunction([], 'throw ' + ref) +\n            ')');\n    } else {\n      this.stack.push(id);\n      const result = this.serialize(fulfilled);\n      this.stack.pop();\n      // just inline the value/reference here\n      serialized = promiseConstructor + '(' + result + ')';\n    }\n    return this.assignIndexedValue(id, serialized);\n  }\n\n  protected serializeWellKnownSymbol(node: SerovalWKSymbolNode): string {\n    return this.assignIndexedValue(node.i, SYMBOL_STRING[node.s]);\n  }\n\n  protected serializeBoxed(node: SerovalBoxedNode): string {\n    return this.assignIndexedValue(\n      node.i,\n      'Object(' + this.serialize(node.f) + ')',\n    );\n  }\n\n  protected serializePlugin(node: SerovalPluginNode): string {\n    const currentPlugins = this.plugins;\n    if (currentPlugins) {\n      for (let i = 0, len = currentPlugins.length; i < len; i++) {\n        const plugin = currentPlugins[i];\n        if (plugin.tag === node.c) {\n          return this.assignIndexedValue(\n            node.i,\n            plugin.serialize(node.s, this, {\n              id: node.i,\n            }),\n          );\n        }\n      }\n    }\n    throw new SerovalMissingPluginError(node.c);\n  }\n\n  private getConstructor(node: SerovalNodeWithID): string {\n    const current = this.serialize(node);\n    return current === this.getRefParam(node.i) ? current : '(' + current + ')';\n  }\n\n  protected serializePromiseConstructor(\n    node: SerovalPromiseConstructorNode,\n  ): string {\n    const resolver = this.assignIndexedValue(node.s, '{p:0,s:0,f:0}');\n    return this.assignIndexedValue(\n      node.i,\n      this.getConstructor(node.f) + '(' + resolver + ')',\n    );\n  }\n\n  protected serializePromiseResolve(node: SerovalPromiseResolveNode): string {\n    return (\n      this.getConstructor(node.a[0]) +\n      '(' +\n      this.getRefParam(node.i) +\n      ',' +\n      this.serialize(node.a[1]) +\n      ')'\n    );\n  }\n\n  protected serializePromiseReject(node: SerovalPromiseRejectNode): string {\n    return (\n      this.getConstructor(node.a[0]) +\n      '(' +\n      this.getRefParam(node.i) +\n      ',' +\n      this.serialize(node.a[1]) +\n      ')'\n    );\n  }\n\n  protected serializeSpecialReference(\n    node: SerovalSpecialReferenceNode,\n  ): string {\n    return this.assignIndexedValue(\n      node.i,\n      serializeSpecialReferenceValue(this.features, node.s),\n    );\n  }\n\n  protected serializeIteratorFactory(node: SerovalIteratorFactoryNode): string {\n    let result = '';\n    let initialized = false;\n    if (node.f.t !== SerovalNodeType.IndexedValue) {\n      this.markRef(node.f.i);\n      result = '(' + this.serialize(node.f) + ',';\n      initialized = true;\n    }\n    result += this.assignIndexedValue(\n      node.i,\n      this.createFunction(\n        ['s'],\n        this.createFunction(\n          ['i', 'c', 'd', 't'],\n          '(i=0,t={[' +\n            this.getRefParam(node.f.i) +\n            ']:' +\n            this.createFunction([], 't') +\n            ',next:' +\n            this.createEffectfulFunction(\n              [],\n              'if(i>s.d)return{done:!0,value:void 0};if(d=s.v[c=i++],c===s.t)throw d;return{done:c===s.d,value:d}',\n            ) +\n            '})',\n        ),\n      ),\n    );\n    if (initialized) {\n      result += ')';\n    }\n    return result;\n  }\n\n  protected serializeIteratorFactoryInstance(\n    node: SerovalIteratorFactoryInstanceNode,\n  ): string {\n    return (\n      this.getConstructor(node.a[0]) + '(' + this.serialize(node.a[1]) + ')'\n    );\n  }\n\n  protected serializeAsyncIteratorFactory(\n    node: SerovalAsyncIteratorFactoryNode,\n  ): string {\n    const promise = node.a[0];\n    const symbol = node.a[1];\n\n    let result = '';\n\n    if (promise.t !== SerovalNodeType.IndexedValue) {\n      this.markRef(promise.i);\n      result += '(' + this.serialize(promise);\n    }\n    if (symbol.t !== SerovalNodeType.IndexedValue) {\n      this.markRef(symbol.i);\n      result += (result ? ',' : '(') + this.serialize(symbol);\n    }\n    if (result) {\n      result += ',';\n    }\n\n    const iterator = this.assignIndexedValue(\n      node.i,\n      this.createFunction(\n        ['s'],\n        this.createFunction(\n          ['b', 'c', 'p', 'd', 'e', 't', 'f'],\n          /**\n           * b = resolved values\n           * c = b size\n           * p = pending promises\n           * d = index where the resolved value stops\n           * e = if the last value is a throw\n           * t = placeholder variable\n           * f = finalize\n           */\n          '(b=[],c=0,p=[],d=-1,e=!1,f=' +\n            this.createEffectfulFunction(\n              ['i', 'l'],\n              'for(i=0,l=p.length;i<l;i++)p[i].s({done:!0,value:void 0})',\n            ) +\n            ',s.on({next:' +\n            this.createEffectfulFunction(\n              ['v', 't'],\n              'if(t=p.shift())t.s({done:!1,value:v});b.push(v)',\n            ) +\n            ',throw:' +\n            this.createEffectfulFunction(\n              ['v', 't'],\n              'if(t=p.shift())t.f(v);f(),d=b.length,e=!0,b.push(v)',\n            ) +\n            ',return:' +\n            this.createEffectfulFunction(\n              ['v', 't'],\n              'if(t=p.shift())t.s({done:!0,value:v});f(),d=b.length,b.push(v)',\n            ) +\n            '}),t={[' +\n            this.getRefParam(symbol.i) +\n            ']:' +\n            this.createFunction([], 't.p') +\n            ',next:' +\n            this.createEffectfulFunction(\n              ['i', 't', 'v'],\n              'if(d===-1){return((i=c++)>=b.length)?(' +\n                this.getRefParam(promise.i) +\n                '(t={p:0,s:0,f:0}),p.push(t),t.p):{done:!1,value:b[i]}}if(c>d)return{done:!0,value:void 0};if(v=b[i=c++],i!==d)return{done:!1,value:v};if(e)throw v;return{done:!0,value:v}',\n            ) +\n            '})',\n        ),\n      ),\n    );\n\n    if (result) {\n      return result + iterator + ')';\n    }\n\n    return iterator;\n  }\n\n  protected serializeAsyncIteratorFactoryInstance(\n    node: SerovalAsyncIteratorFactoryInstanceNode,\n  ): string {\n    return (\n      this.getConstructor(node.a[0]) + '(' + this.serialize(node.a[1]) + ')'\n    );\n  }\n\n  protected serializeStreamConstructor(\n    node: SerovalStreamConstructorNode,\n  ): string {\n    const result = this.assignIndexedValue(\n      node.i,\n      this.getConstructor(node.f) + '()',\n    );\n    const len = node.a.length;\n    if (len) {\n      let values = this.serialize(node.a[0]);\n      for (let i = 1; i < len; i++) {\n        values += ',' + this.serialize(node.a[i]);\n      }\n      return '(' + result + ',' + values + ',' + this.getRefParam(node.i) + ')';\n    }\n    return result;\n  }\n\n  protected serializeStreamNext(node: SerovalStreamNextNode): string {\n    return this.getRefParam(node.i) + '.next(' + this.serialize(node.f) + ')';\n  }\n\n  protected serializeStreamThrow(node: SerovalStreamThrowNode): string {\n    return this.getRefParam(node.i) + '.throw(' + this.serialize(node.f) + ')';\n  }\n\n  protected serializeStreamReturn(node: SerovalStreamReturnNode): string {\n    return this.getRefParam(node.i) + '.return(' + this.serialize(node.f) + ')';\n  }\n\n  serialize(node: SerovalNode): string {\n    try {\n      switch (node.t) {\n        case SerovalNodeType.Constant:\n          return CONSTANT_STRING[node.s];\n        case SerovalNodeType.Number:\n          return '' + node.s;\n        case SerovalNodeType.String:\n          return '\"' + node.s + '\"';\n        case SerovalNodeType.BigInt:\n          return node.s + 'n';\n        case SerovalNodeType.IndexedValue:\n          return this.getRefParam(node.i);\n        case SerovalNodeType.Reference:\n          return this.serializeReference(node);\n        case SerovalNodeType.Array:\n          return this.serializeArray(node);\n        case SerovalNodeType.Object:\n          return this.serializeObject(node);\n        case SerovalNodeType.NullConstructor:\n          return this.serializeNullConstructor(node);\n        case SerovalNodeType.Date:\n          return this.serializeDate(node);\n        case SerovalNodeType.RegExp:\n          return this.serializeRegExp(node);\n        case SerovalNodeType.Set:\n          return this.serializeSet(node);\n        case SerovalNodeType.Map:\n          return this.serializeMap(node);\n        case SerovalNodeType.ArrayBuffer:\n          return this.serializeArrayBuffer(node);\n        case SerovalNodeType.BigIntTypedArray:\n        case SerovalNodeType.TypedArray:\n          return this.serializeTypedArray(node);\n        case SerovalNodeType.DataView:\n          return this.serializeDataView(node);\n        case SerovalNodeType.AggregateError:\n          return this.serializeAggregateError(node);\n        case SerovalNodeType.Error:\n          return this.serializeError(node);\n        case SerovalNodeType.Promise:\n          return this.serializePromise(node);\n        case SerovalNodeType.WKSymbol:\n          return this.serializeWellKnownSymbol(node);\n        case SerovalNodeType.Boxed:\n          return this.serializeBoxed(node);\n        case SerovalNodeType.PromiseConstructor:\n          return this.serializePromiseConstructor(node);\n        case SerovalNodeType.PromiseSuccess:\n          return this.serializePromiseResolve(node);\n        case SerovalNodeType.PromiseFailure:\n          return this.serializePromiseReject(node);\n        case SerovalNodeType.Plugin:\n          return this.serializePlugin(node);\n        case SerovalNodeType.SpecialReference:\n          return this.serializeSpecialReference(node);\n        case SerovalNodeType.IteratorFactory:\n          return this.serializeIteratorFactory(node);\n        case SerovalNodeType.IteratorFactoryInstance:\n          return this.serializeIteratorFactoryInstance(node);\n        case SerovalNodeType.AsyncIteratorFactory:\n          return this.serializeAsyncIteratorFactory(node);\n        case SerovalNodeType.AsyncIteratorFactoryInstance:\n          return this.serializeAsyncIteratorFactoryInstance(node);\n        case SerovalNodeType.StreamConstructor:\n          return this.serializeStreamConstructor(node);\n        case SerovalNodeType.StreamNext:\n          return this.serializeStreamNext(node);\n        case SerovalNodeType.StreamThrow:\n          return this.serializeStreamThrow(node);\n        case SerovalNodeType.StreamReturn:\n          return this.serializeStreamReturn(node);\n        default:\n          throw new SerovalUnsupportedNodeError(node);\n      }\n    } catch (error) {\n      throw new SerovalSerializationError(error);\n    }\n  }\n}\n", "import { SerovalNodeType } from '../constants';\nimport type { BaseSerializerContextOptions } from '../context/serializer';\nimport BaseSerializerContext from '../context/serializer';\nimport { GLOBAL_CONTEXT_REFERENCES } from '../keys';\nimport type { SerovalMode } from '../plugin';\nimport { serializeString } from '../string';\nimport type { SerovalNode } from '../types';\nimport type { CrossContextOptions } from './parser';\n\nexport interface CrossSerializerContextOptions\n  extends BaseSerializerContextOptions,\n    CrossContextOptions {}\n\nexport default class CrossSerializerContext extends BaseSerializerContext {\n  readonly mode: SerovalMode = 'cross';\n\n  scopeId?: string;\n\n  constructor(options: CrossSerializerContextOptions) {\n    super(options);\n    this.scopeId = options.scopeId;\n  }\n\n  getRefParam(id: number): string {\n    return GLOBAL_CONTEXT_REFERENCES + '[' + id + ']';\n  }\n\n  protected assignIndexedValue(index: number, value: string): string {\n    // In cross-reference, we have to assume that\n    // every reference are going to be referenced\n    // in the future, and so we need to store\n    // all of it into the reference array.\n    return this.getRefParam(index) + '=' + value;\n  }\n\n  serializeTop(tree: SerovalNode): string {\n    // Get the serialized result\n    const result = this.serialize(tree);\n    // If the node is a non-reference, return\n    // the result immediately\n    const id = tree.i;\n    if (id == null) {\n      return result;\n    }\n    // Get the patches\n    const patches = this.resolvePatches();\n    // Get the variable that represents the root\n    const ref = this.getRefParam(id);\n    // Parameters needed for scoping\n    const params = this.scopeId == null ? '' : GLOBAL_CONTEXT_REFERENCES;\n    // If there are patches, append it after the result\n    const body = patches ? '(' + result + ',' + patches + ref + ')' : result;\n    // If there are no params, there's no need to generate a function\n    if (params === '') {\n      if (tree.t === SerovalNodeType.Object && !patches) {\n        return '(' + body + ')';\n      }\n      return body;\n    }\n    // Get the arguments for the IIFE\n    const args =\n      this.scopeId == null\n        ? '()'\n        : '(' +\n          GLOBAL_CONTEXT_REFERENCES +\n          '[\"' +\n          serializeString(this.scopeId) +\n          '\"])';\n    // Create the IIFE\n    return '(' + this.createFunction([params], body) + ')' + args;\n  }\n}\n", "import {\n  createAggregateErrorNode,\n  create<PERSON><PERSON>yBuffer<PERSON><PERSON>,\n  createArrayNode,\n  createAsyncIteratorFactoryInstanceNode,\n  createBigIntNode,\n  createBigIntTypedArrayNode,\n  createBoxedNode,\n  createDataViewNode,\n  createDateNode,\n  createErrorNode,\n  createIteratorFactoryInstanceN<PERSON>,\n  createNumberNode,\n  createPluginNode,\n  createRegExpNode,\n  createSetNode,\n  createStreamConstructorNode,\n  createStringNode,\n  createTypedArrayNode,\n} from '../../base-primitives';\nimport { Feature } from '../../compat';\nimport { NIL } from '../../constants';\nimport { SerovalParserError, SerovalUnsupportedTypeError } from '../../errors';\nimport {\n  FALSE_NODE,\n  NULL_NODE,\n  TRUE_NODE,\n  UNDEFINED_NODE,\n} from '../../literals';\nimport { OpaqueReference } from '../../opaque-reference';\nimport { SpecialReference } from '../../special-reference';\nimport type { Stream } from '../../stream';\nimport { createStream, isStream } from '../../stream';\nimport { serializeString } from '../../string';\nimport type {\n  SerovalAggregateErrorNode,\n  SerovalArrayNode,\n  SerovalBigIntTypedArrayNode,\n  SerovalBoxedNode,\n  SerovalDataViewNode,\n  SerovalErrorNode,\n  SerovalMapNode,\n  SerovalNode,\n  SerovalNullConstructorNode,\n  SerovalObjectNode,\n  SerovalObjectRecordKey,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalPromiseConstructorNode,\n  SerovalSetNode,\n  SerovalTypedArrayNode,\n} from '../../types';\nimport { getErrorOptions } from '../../utils/error';\nimport { iteratorToSequence } from '../../utils/iterator-to-sequence';\nimport type {\n  BigIntTypedArrayValue,\n  TypedArrayValue,\n} from '../../utils/typed-array';\nimport type { BaseParserContextOptions } from '../parser';\nimport { BaseParserContext, ParserNodeType } from '../parser';\n\ntype ObjectLikeNode = SerovalObjectNode | SerovalNullConstructorNode;\n\nexport type BaseSyncParserContextOptions = BaseParserContextOptions;\n\nexport default abstract class BaseSyncParserContext extends BaseParserContext {\n  protected parseItems(current: unknown[]): SerovalNode[] {\n    const nodes = [];\n    for (let i = 0, len = current.length; i < len; i++) {\n      if (i in current) {\n        nodes[i] = this.parse(current[i]);\n      }\n    }\n    return nodes;\n  }\n\n  protected parseArray(id: number, current: unknown[]): SerovalArrayNode {\n    return createArrayNode(id, current, this.parseItems(current));\n  }\n\n  protected parseProperties(\n    properties: Record<string | symbol, unknown>,\n  ): SerovalObjectRecordNode {\n    const entries = Object.entries(properties);\n    const keyNodes: SerovalObjectRecordKey[] = [];\n    const valueNodes: SerovalNode[] = [];\n    for (let i = 0, len = entries.length; i < len; i++) {\n      keyNodes.push(serializeString(entries[i][0]));\n      valueNodes.push(this.parse(entries[i][1]));\n    }\n    // Check special properties, symbols in this case\n    let symbol = Symbol.iterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createIteratorFactoryInstanceNode(\n          this.parseIteratorFactory(),\n          this.parse(\n            iteratorToSequence(properties as unknown as Iterable<unknown>),\n          ),\n        ),\n      );\n    }\n    symbol = Symbol.asyncIterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createAsyncIteratorFactoryInstanceNode(\n          this.parseAsyncIteratorFactory(),\n          this.parse(createStream()),\n        ),\n      );\n    }\n    symbol = Symbol.toStringTag;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(createStringNode(properties[symbol] as string));\n    }\n    symbol = Symbol.isConcatSpreadable;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(properties[symbol] ? TRUE_NODE : FALSE_NODE);\n    }\n    return {\n      k: keyNodes,\n      v: valueNodes,\n      s: keyNodes.length,\n    };\n  }\n\n  protected parsePlainObject(\n    id: number,\n    current: Record<string, unknown>,\n    empty: boolean,\n  ): ObjectLikeNode {\n    return this.createObjectNode(\n      id,\n      current,\n      empty,\n      this.parseProperties(current),\n    );\n  }\n\n  protected parseBoxed(id: number, current: object): SerovalBoxedNode {\n    return createBoxedNode(id, this.parse(current.valueOf()));\n  }\n\n  protected parseTypedArray(\n    id: number,\n    current: TypedArrayValue,\n  ): SerovalTypedArrayNode {\n    return createTypedArrayNode(id, current, this.parse(current.buffer));\n  }\n\n  protected parseBigIntTypedArray(\n    id: number,\n    current: BigIntTypedArrayValue,\n  ): SerovalBigIntTypedArrayNode {\n    return createBigIntTypedArrayNode(id, current, this.parse(current.buffer));\n  }\n\n  protected parseDataView(id: number, current: DataView): SerovalDataViewNode {\n    return createDataViewNode(id, current, this.parse(current.buffer));\n  }\n\n  protected parseError(id: number, current: Error): SerovalErrorNode {\n    const options = getErrorOptions(current, this.features);\n    return createErrorNode(\n      id,\n      current,\n      options ? this.parseProperties(options) : NIL,\n    );\n  }\n\n  protected parseAggregateError(\n    id: number,\n    current: AggregateError,\n  ): SerovalAggregateErrorNode {\n    const options = getErrorOptions(current, this.features);\n    return createAggregateErrorNode(\n      id,\n      current,\n      options ? this.parseProperties(options) : NIL,\n    );\n  }\n\n  protected parseMap(\n    id: number,\n    current: Map<unknown, unknown>,\n  ): SerovalMapNode {\n    const keyNodes: SerovalNode[] = [];\n    const valueNodes: SerovalNode[] = [];\n    for (const [key, value] of current.entries()) {\n      keyNodes.push(this.parse(key));\n      valueNodes.push(this.parse(value));\n    }\n    return this.createMapNode(id, keyNodes, valueNodes, current.size);\n  }\n\n  protected parseSet(id: number, current: Set<unknown>): SerovalSetNode {\n    const items: SerovalNode[] = [];\n    for (const item of current.keys()) {\n      items.push(this.parse(item));\n    }\n    return createSetNode(id, current.size, items);\n  }\n\n  protected parsePlugin(\n    id: number,\n    current: unknown,\n  ): SerovalPluginNode | undefined {\n    const currentPlugins = this.plugins;\n    if (currentPlugins) {\n      for (let i = 0, len = currentPlugins.length; i < len; i++) {\n        const plugin = currentPlugins[i];\n        if (plugin.parse.sync && plugin.test(current)) {\n          return createPluginNode(\n            id,\n            plugin.tag,\n            plugin.parse.sync(current, this, {\n              id,\n            }),\n          );\n        }\n      }\n    }\n    return undefined;\n  }\n\n  protected parseStream(id: number, _current: Stream<unknown>): SerovalNode {\n    return createStreamConstructorNode(\n      id,\n      this.parseSpecialReference(SpecialReference.StreamConstructor),\n      [],\n    );\n  }\n\n  protected parsePromise(\n    id: number,\n    _current: Promise<unknown>,\n  ): SerovalPromiseConstructorNode {\n    return this.createPromiseConstructorNode(id, this.createIndex({}));\n  }\n\n  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: ehh\n  protected parseObject(id: number, current: object): SerovalNode {\n    if (Array.isArray(current)) {\n      return this.parseArray(id, current);\n    }\n    if (isStream(current)) {\n      return this.parseStream(id, current);\n    }\n    const currentClass = current.constructor;\n    if (currentClass === OpaqueReference) {\n      return this.parse(\n        (current as OpaqueReference<unknown, unknown>).replacement,\n      );\n    }\n    const parsed = this.parsePlugin(id, current);\n    if (parsed) {\n      return parsed;\n    }\n    switch (currentClass) {\n      case Object:\n        return this.parsePlainObject(\n          id,\n          current as Record<string, unknown>,\n          false,\n        );\n      case undefined:\n        return this.parsePlainObject(\n          id,\n          current as Record<string, unknown>,\n          true,\n        );\n      case Date:\n        return createDateNode(id, current as unknown as Date);\n      case RegExp:\n        return createRegExpNode(id, current as unknown as RegExp);\n      case Error:\n      case EvalError:\n      case RangeError:\n      case ReferenceError:\n      case SyntaxError:\n      case TypeError:\n      case URIError:\n        return this.parseError(id, current as unknown as Error);\n      case Number:\n      case Boolean:\n      case String:\n      case BigInt:\n        return this.parseBoxed(id, current);\n      case ArrayBuffer:\n        return createArrayBufferNode(id, current as unknown as ArrayBuffer);\n      case Int8Array:\n      case Int16Array:\n      case Int32Array:\n      case Uint8Array:\n      case Uint16Array:\n      case Uint32Array:\n      case Uint8ClampedArray:\n      case Float32Array:\n      case Float64Array:\n        return this.parseTypedArray(id, current as unknown as TypedArrayValue);\n      case DataView:\n        return this.parseDataView(id, current as unknown as DataView);\n      case Map:\n        return this.parseMap(id, current as unknown as Map<unknown, unknown>);\n      case Set:\n        return this.parseSet(id, current as unknown as Set<unknown>);\n      default:\n        break;\n    }\n    // Promises\n    if (currentClass === Promise || current instanceof Promise) {\n      return this.parsePromise(id, current as unknown as Promise<unknown>);\n    }\n    const currentFeatures = this.features;\n    // BigInt Typed Arrays\n    if (currentFeatures & Feature.BigIntTypedArray) {\n      switch (currentClass) {\n        case BigInt64Array:\n        case BigUint64Array:\n          return this.parseBigIntTypedArray(\n            id,\n            current as unknown as BigIntTypedArrayValue,\n          );\n        default:\n          break;\n      }\n    }\n    if (\n      currentFeatures & Feature.AggregateError &&\n      typeof AggregateError !== 'undefined' &&\n      (currentClass === AggregateError || current instanceof AggregateError)\n    ) {\n      return this.parseAggregateError(id, current as unknown as AggregateError);\n    }\n    // Slow path. We only need to handle Errors and Iterators\n    // since they have very broad implementations.\n    if (current instanceof Error) {\n      return this.parseError(id, current);\n    }\n    // Generator functions don't have a global constructor\n    // despite existing\n    if (Symbol.iterator in current || Symbol.asyncIterator in current) {\n      return this.parsePlainObject(id, current, !!currentClass);\n    }\n    throw new SerovalUnsupportedTypeError(current);\n  }\n\n  protected parseFunction(current: unknown): SerovalNode {\n    const ref = this.getReference(current);\n    if (ref.type !== ParserNodeType.Fresh) {\n      return ref.value;\n    }\n    const plugin = this.parsePlugin(ref.value, current);\n    if (plugin) {\n      return plugin;\n    }\n    throw new SerovalUnsupportedTypeError(current);\n  }\n\n  parse<T>(current: T): SerovalNode {\n    switch (typeof current) {\n      case 'boolean':\n        return current ? TRUE_NODE : FALSE_NODE;\n      case 'undefined':\n        return UNDEFINED_NODE;\n      case 'string':\n        return createStringNode(current as string);\n      case 'number':\n        return createNumberNode(current as number);\n      case 'bigint':\n        return createBigIntNode(current as bigint);\n      case 'object': {\n        if (current) {\n          const ref = this.getReference(current);\n          return ref.type === ParserNodeType.Fresh\n            ? this.parseObject(ref.value, current as object)\n            : ref.value;\n        }\n        return NULL_NODE;\n      }\n      case 'symbol':\n        return this.parseWellKnownSymbol(current);\n      case 'function': {\n        return this.parseFunction(current);\n      }\n      default:\n        throw new SerovalUnsupportedTypeError(current);\n    }\n  }\n\n  parseTop<T>(current: T): SerovalNode {\n    try {\n      return this.parse(current);\n    } catch (error) {\n      throw error instanceof SerovalParserError\n        ? error\n        : new SerovalParserError(error);\n    }\n  }\n}\n", "import {\n  createAsyncIteratorFactoryInstanceNode,\n  createIteratorFactoryInstanceNode,\n  createPluginNode,\n  createStreamConstructorNode,\n  createStreamNextNode,\n  createStreamReturnNode,\n  createStreamThrowNode,\n  createStringNode,\n} from '../../base-primitives';\nimport { NIL, SerovalNodeType } from '../../constants';\nimport { FALSE_NODE, TRUE_NODE } from '../../literals';\nimport { createSerovalNode } from '../../node';\nimport { SpecialReference } from '../../special-reference';\nimport type { Stream } from '../../stream';\nimport { createStreamFromAsyncIterable } from '../../stream';\nimport { serializeString } from '../../string';\nimport type {\n  SerovalNode,\n  SerovalObjectRecordKey,\n  SerovalObjectRecordNode,\n  SerovalPluginNode,\n  SerovalPromiseConstructorNode,\n} from '../../types';\nimport { iteratorToSequence } from '../../utils/iterator-to-sequence';\nimport type { BaseSyncParserContextOptions } from './sync';\nimport BaseSyncParserContext from './sync';\n\nexport interface BaseStreamParserContextOptions\n  extends BaseSyncParserContextOptions {\n  onParse: (node: SerovalNode, initial: boolean) => void;\n  onError?: (error: unknown) => void;\n  onDone?: () => void;\n}\n\nexport default abstract class BaseStreamParserContext extends BaseSyncParserContext {\n  // Life\n  private alive = true;\n\n  // Amount of pending promises/streams\n  private pending = 0;\n\n  private onParseCallback: (node: SerovalNode, initial: boolean) => void;\n\n  private onErrorCallback?: (error: unknown) => void;\n\n  private onDoneCallback?: () => void;\n\n  constructor(options: BaseStreamParserContextOptions) {\n    super(options);\n    this.onParseCallback = options.onParse;\n    this.onErrorCallback = options.onError;\n    this.onDoneCallback = options.onDone;\n  }\n\n  private initial = true;\n\n  private buffer: SerovalNode[] = [];\n\n  private onParseInternal(node: SerovalNode, initial: boolean): void {\n    try {\n      this.onParseCallback(node, initial);\n    } catch (error) {\n      this.onError(error);\n    }\n  }\n\n  private flush(): void {\n    for (let i = 0, len = this.buffer.length; i < len; i++) {\n      this.onParseInternal(this.buffer[i], false);\n    }\n  }\n\n  onParse(node: SerovalNode): void {\n    if (this.initial) {\n      this.buffer.push(node);\n    } else {\n      this.onParseInternal(node, false);\n    }\n  }\n\n  onError(error: unknown): void {\n    if (this.onErrorCallback) {\n      this.onErrorCallback(error);\n    } else {\n      throw error;\n    }\n  }\n\n  private onDone(): void {\n    if (this.onDoneCallback) {\n      this.onDoneCallback();\n    }\n  }\n\n  pushPendingState(): void {\n    this.pending++;\n  }\n\n  popPendingState(): void {\n    if (--this.pending <= 0) {\n      this.onDone();\n    }\n  }\n\n  protected parseProperties(\n    properties: Record<string | symbol, unknown>,\n  ): SerovalObjectRecordNode {\n    const entries = Object.entries(properties);\n    const keyNodes: SerovalObjectRecordKey[] = [];\n    const valueNodes: SerovalNode[] = [];\n    for (let i = 0, len = entries.length; i < len; i++) {\n      keyNodes.push(serializeString(entries[i][0]));\n      valueNodes.push(this.parse(entries[i][1]));\n    }\n    // Check special properties, symbols in this case\n    let symbol = Symbol.iterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createIteratorFactoryInstanceNode(\n          this.parseIteratorFactory(),\n          this.parse(\n            iteratorToSequence(properties as unknown as Iterable<unknown>),\n          ),\n        ),\n      );\n    }\n    symbol = Symbol.asyncIterator;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(\n        createAsyncIteratorFactoryInstanceNode(\n          this.parseAsyncIteratorFactory(),\n          this.parse(\n            createStreamFromAsyncIterable(\n              properties as unknown as AsyncIterable<unknown>,\n            ),\n          ),\n        ),\n      );\n    }\n    symbol = Symbol.toStringTag;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(createStringNode(properties[symbol] as string));\n    }\n    symbol = Symbol.isConcatSpreadable;\n    if (symbol in properties) {\n      keyNodes.push(this.parseWellKnownSymbol(symbol));\n      valueNodes.push(properties[symbol] ? TRUE_NODE : FALSE_NODE);\n    }\n    return {\n      k: keyNodes,\n      v: valueNodes,\n      s: keyNodes.length,\n    };\n  }\n\n  protected handlePromiseSuccess(id: number, data: unknown): void {\n    const parsed = this.parseWithError(data);\n    if (parsed) {\n      this.onParse(\n        createSerovalNode(\n          SerovalNodeType.PromiseSuccess,\n          id,\n          NIL,\n          NIL,\n          NIL,\n          NIL,\n          NIL,\n          NIL,\n          [this.parseSpecialReference(SpecialReference.PromiseSuccess), parsed],\n          NIL,\n          NIL,\n          NIL,\n        ),\n      );\n    }\n    this.popPendingState();\n  }\n\n  protected handlePromiseFailure(id: number, data: unknown): void {\n    if (this.alive) {\n      const parsed = this.parseWithError(data);\n      if (parsed) {\n        this.onParse(\n          createSerovalNode(\n            SerovalNodeType.PromiseFailure,\n            id,\n            NIL,\n            NIL,\n            NIL,\n            NIL,\n            NIL,\n            NIL,\n            [\n              this.parseSpecialReference(SpecialReference.PromiseFailure),\n              parsed,\n            ],\n            NIL,\n            NIL,\n            NIL,\n          ),\n        );\n      }\n    }\n    this.popPendingState();\n  }\n\n  protected parsePromise(\n    id: number,\n    current: Promise<unknown>,\n  ): SerovalPromiseConstructorNode {\n    const resolver = this.createIndex({});\n    current.then(\n      this.handlePromiseSuccess.bind(this, resolver),\n      this.handlePromiseFailure.bind(this, resolver),\n    );\n    this.pushPendingState();\n    return this.createPromiseConstructorNode(id, resolver);\n  }\n\n  protected parsePlugin(\n    id: number,\n    current: unknown,\n  ): SerovalPluginNode | undefined {\n    const currentPlugins = this.plugins;\n    if (currentPlugins) {\n      for (let i = 0, len = currentPlugins.length; i < len; i++) {\n        const plugin = currentPlugins[i];\n        if (plugin.parse.stream && plugin.test(current)) {\n          return createPluginNode(\n            id,\n            plugin.tag,\n            plugin.parse.stream(current, this, {\n              id,\n            }),\n          );\n        }\n      }\n    }\n    return NIL;\n  }\n\n  protected parseStream(id: number, current: Stream<unknown>): SerovalNode {\n    const result = createStreamConstructorNode(\n      id,\n      this.parseSpecialReference(SpecialReference.StreamConstructor),\n      [],\n    );\n    this.pushPendingState();\n    current.on({\n      next: value => {\n        if (this.alive) {\n          const parsed = this.parseWithError(value);\n          if (parsed) {\n            this.onParse(createStreamNextNode(id, parsed));\n          }\n        }\n      },\n      throw: value => {\n        if (this.alive) {\n          const parsed = this.parseWithError(value);\n          if (parsed) {\n            this.onParse(createStreamThrowNode(id, parsed));\n          }\n        }\n        this.popPendingState();\n      },\n      return: value => {\n        if (this.alive) {\n          const parsed = this.parseWithError(value);\n          if (parsed) {\n            this.onParse(createStreamReturnNode(id, parsed));\n          }\n        }\n        this.popPendingState();\n      },\n    });\n    return result;\n  }\n\n  parseWithError<T>(current: T): SerovalNode | undefined {\n    try {\n      return this.parse(current);\n    } catch (err) {\n      this.onError(err);\n      return NIL;\n    }\n  }\n\n  /**\n   * @private\n   */\n  start<T>(current: T): void {\n    const parsed = this.parseWithError(current);\n    if (parsed) {\n      this.onParseInternal(parsed, true);\n      this.initial = false;\n      this.flush();\n\n      // Check if there's any pending pushes\n      if (this.pending <= 0) {\n        this.destroy();\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  destroy(): void {\n    if (this.alive) {\n      this.onDone();\n      this.alive = false;\n    }\n  }\n\n  isAlive(): boolean {\n    return this.alive;\n  }\n}\n", "import type { BaseStreamParserContextOptions } from '../context/parser/stream';\nimport BaseStreamParserContext from '../context/parser/stream';\nimport type { SerovalMode } from '../plugin';\n\nexport type CrossStreamParserContextOptions = BaseStreamParserContextOptions;\n\nexport default class CrossStreamParserContext extends BaseStreamParserContext {\n  readonly mode: SerovalMode = 'cross';\n}\n", "import BaseSyncParserContext from '../context/parser/sync';\nimport type { SerovalMode } from '../plugin';\nimport type { CrossParserContextOptions } from './parser';\n\nexport type CrossSyncParserContextOptions = CrossParserContextOptions;\n\nexport default class CrossSyncParserContext extends BaseSyncParserContext {\n  readonly mode: SerovalMode = 'cross';\n}\n", "import { resolvePlugins } from '../plugin';\nimport type { SerovalNode } from '../types';\nimport type { CrossAsyncParserContextOptions } from './async';\nimport AsyncCrossParserContext from './async';\nimport type { CrossDeserializerContextOptions } from './deserializer';\nimport CrossDeserializerContext from './deserializer';\nimport type { CrossContextOptions, CrossParserContextOptions } from './parser';\nimport CrossSerializerContext from './serializer';\nimport type { CrossStreamParserContextOptions } from './stream';\nimport StreamCrossParserContext from './stream';\nimport type { CrossSyncParserContextOptions } from './sync';\nimport SyncCrossParserContext from './sync';\n\nexport interface CrossSerializeOptions\n  extends CrossSyncParserContextOptions,\n    CrossContextOptions {}\n\nexport function crossSerialize<T>(\n  source: T,\n  options: CrossSerializeOptions = {},\n): string {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new SyncCrossParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n    refs: options.refs,\n  });\n  const tree = ctx.parseTop(source);\n  const serial = new CrossSerializerContext({\n    plugins,\n    features: ctx.features,\n    scopeId: options.scopeId,\n    markedRefs: ctx.marked,\n  });\n  return serial.serializeTop(tree);\n}\n\nexport interface CrossSerializeAsyncOptions\n  extends CrossAsyncParserContextOptions,\n    CrossContextOptions {}\n\nexport async function crossSerializeAsync<T>(\n  source: T,\n  options: CrossSerializeAsyncOptions = {},\n): Promise<string> {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new AsyncCrossParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n    refs: options.refs,\n  });\n  const tree = await ctx.parseTop(source);\n  const serial = new CrossSerializerContext({\n    plugins,\n    features: ctx.features,\n    scopeId: options.scopeId,\n    markedRefs: ctx.marked,\n  });\n  return serial.serializeTop(tree);\n}\n\nexport type ToCrossJSONOptions = CrossParserContextOptions;\n\nexport function toCrossJSON<T>(\n  source: T,\n  options: CrossParserContextOptions = {},\n): SerovalNode {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new SyncCrossParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n    refs: options.refs,\n  });\n  return ctx.parseTop(source);\n}\n\nexport type ToCrossJSONAsyncOptions = CrossParserContextOptions;\n\nexport async function toCrossJSONAsync<T>(\n  source: T,\n  options: CrossParserContextOptions = {},\n): Promise<SerovalNode> {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new AsyncCrossParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n    refs: options.refs,\n  });\n  return await ctx.parseTop(source);\n}\n\nexport interface CrossSerializeStreamOptions\n  extends Omit<CrossStreamParserContextOptions, 'onParse'>,\n    CrossContextOptions {\n  onSerialize: (data: string, initial: boolean) => void;\n}\n\nexport function crossSerializeStream<T>(\n  source: T,\n  options: CrossSerializeStreamOptions,\n): () => void {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new StreamCrossParserContext({\n    plugins,\n    refs: options.refs,\n    disabledFeatures: options.disabledFeatures,\n    onParse(node, initial): void {\n      const serial = new CrossSerializerContext({\n        plugins,\n        features: ctx.features,\n        scopeId: options.scopeId,\n        markedRefs: ctx.marked,\n      });\n\n      let serialized: string;\n\n      try {\n        serialized = serial.serializeTop(node);\n      } catch (err) {\n        if (options.onError) {\n          options.onError(err);\n        }\n        return;\n      }\n\n      options.onSerialize(serialized, initial);\n    },\n    onError: options.onError,\n    onDone: options.onDone,\n  });\n\n  ctx.start(source);\n\n  return ctx.destroy.bind(ctx);\n}\n\nexport type ToCrossJSONStreamOptions = CrossStreamParserContextOptions;\n\nexport function toCrossJSONStream<T>(\n  source: T,\n  options: ToCrossJSONStreamOptions,\n): () => void {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new StreamCrossParserContext({\n    plugins,\n    refs: options.refs,\n    disabledFeatures: options.disabledFeatures,\n    onParse: options.onParse,\n    onError: options.onError,\n    onDone: options.onDone,\n  });\n\n  ctx.start(source);\n\n  return ctx.destroy.bind(ctx);\n}\n\nexport type FromCrossJSONOptions = CrossDeserializerContextOptions;\n\nexport function fromCrossJSON<T>(\n  source: SerovalNode,\n  options: FromCrossJSONOptions,\n): T {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new CrossDeserializerContext({\n    plugins,\n    refs: options.refs,\n  });\n  return ctx.deserializeTop(source) as T;\n}\n", "import type { BaseParserContextOptions } from '../context/parser';\nimport BaseAsyncParserContext from '../context/parser/async';\nimport type { SerovalMode } from '../plugin';\n\nexport type AsyncParserContextOptions = Omit<BaseParserContextOptions, 'refs'>;\n\nexport default class AsyncParserContext extends BaseAsyncParserContext {\n  readonly mode: SerovalMode = 'vanilla';\n}\n", "import type { BaseDeserializerOptions } from '../context/deserializer';\nimport BaseDeserializerContext from '../context/deserializer';\nimport type { SerovalMode } from '../plugin';\n\nexport interface VanillaDeserializerContextOptions\n  extends Omit<BaseDeserializerOptions, 'refs'> {\n  markedRefs: number[] | Set<number>;\n}\n\nexport default class VanillaDeserializerContext extends BaseDeserializerContext {\n  readonly mode: SerovalMode = 'vanilla';\n\n  marked: Set<number>;\n\n  constructor(options: VanillaDeserializerContextOptions) {\n    super(options);\n    this.marked = new Set(options.markedRefs);\n  }\n\n  assignIndexedValue<T>(index: number, value: T): T {\n    if (this.marked.has(index)) {\n      this.refs.set(index, value);\n    }\n    return value;\n  }\n}\n", "// Written by https://github.com/<PERSON><PERSON><PERSON><PERSON> and is distributed under the MIT license.\nconst REF_START_CHARS = /* @__PURE__ */ 'hjkmoquxzABCDEFGHIJKLNPQRTUVWXYZ$_'; // Avoids chars that could evaluate to a reserved word.\nconst REF_START_CHARS_LEN = /* @__PURE__ */ REF_START_CHARS.length;\nconst REF_CHARS =\n  /* @__PURE__ */ 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$_';\nconst REF_CHARS_LEN = /* @__PURE__ */ REF_CHARS.length;\n\nexport default function getIdentifier(index: number): string {\n  let mod = index % REF_START_CHARS_LEN;\n  let ref = REF_START_CHARS[mod];\n  index = (index - mod) / REF_START_CHARS_LEN;\n  while (index > 0) {\n    mod = index % REF_CHARS_LEN;\n    ref += REF_CHARS[mod];\n    index = (index - mod) / REF_CHARS_LEN;\n  }\n  return ref;\n}\n", "import { SerovalNodeType } from '../constants';\nimport type { BaseSerializerContextOptions } from '../context/serializer';\nimport BaseSerializerContext from '../context/serializer';\nimport { SerovalUnsupportedNodeError } from '../errors';\nimport type { SerovalMode } from '../plugin';\nimport type {\n  SerovalNode,\n  SerovalPromiseConstructorNode,\n  SerovalPromiseRejectNode,\n  SerovalPromiseResolveNode,\n} from '../types';\nimport getIdentifier from '../utils/get-identifier';\n\nexport type VanillaSerializerContextOptions = BaseSerializerContextOptions;\n\nexport default class VanillaSerializerContext extends BaseSerializerContext {\n  readonly mode: SerovalMode = 'vanilla';\n\n  /**\n   * Map tree refs to actual refs\n   * @private\n   */\n  valid = new Map<number, number>();\n\n  /**\n   * Variables\n   * @private\n   */\n  vars: string[] = [];\n\n  /**\n   * Creates the reference param (identifier) from the given reference ID\n   * Calling this function means the value has been referenced somewhere\n   */\n  getRefParam(index: number): string {\n    /**\n     * Creates a new reference ID from a given reference ID\n     * This new reference ID means that the reference itself\n     * has been referenced at least once, and is used to generate\n     * the variables\n     */\n    let actualIndex = this.valid.get(index);\n    if (actualIndex == null) {\n      actualIndex = this.valid.size;\n      this.valid.set(index, actualIndex);\n    }\n    let identifier = this.vars[actualIndex];\n    if (identifier == null) {\n      identifier = getIdentifier(actualIndex);\n      this.vars[actualIndex] = identifier;\n    }\n    return identifier;\n  }\n\n  protected assignIndexedValue(index: number, value: string): string {\n    if (this.isMarked(index)) {\n      return this.getRefParam(index) + '=' + value;\n    }\n    return value;\n  }\n\n  protected serializePromiseConstructor(\n    node: SerovalPromiseConstructorNode,\n  ): string {\n    throw new SerovalUnsupportedNodeError(node);\n  }\n\n  protected serializePromiseResolve(node: SerovalPromiseResolveNode): string {\n    throw new SerovalUnsupportedNodeError(node);\n  }\n\n  protected serializePromiseReject(node: SerovalPromiseRejectNode): string {\n    throw new SerovalUnsupportedNodeError(node);\n  }\n\n  serializeTop(tree: SerovalNode): string {\n    const result = this.serialize(tree);\n    // Shared references detected\n    if (tree.i != null && this.vars.length) {\n      const patches = this.resolvePatches();\n      let body = result;\n      if (patches) {\n        // Get (or create) a ref from the source\n        const index = this.getRefParam(tree.i);\n        body = result + ',' + patches + index;\n        if (!result.startsWith(index + '=')) {\n          body = index + '=' + body;\n        }\n        body = '(' + body + ')';\n      }\n      return '(' + this.createFunction(this.vars, body) + ')()';\n    }\n    if (tree.t === SerovalNodeType.Object) {\n      return '(' + result + ')';\n    }\n    return result;\n  }\n}\n", "import BaseSyncParserContext from '../context/parser/sync';\nimport type { BaseParserContextOptions } from '../context/parser';\nimport type { SerovalMode } from '../plugin';\n\nexport type SyncParserContextOptions = Omit<BaseParserContextOptions, 'refs'>;\n\nexport default class SyncParserContext extends BaseSyncParserContext {\n  readonly mode: SerovalMode = 'vanilla';\n}\n", "import { type PluginAccessOptions, resolvePlugins } from '../plugin';\nimport type { SerovalNode } from '../types';\nimport type { AsyncParserContextOptions } from './async';\nimport AsyncParserContext from './async';\nimport VanillaDeserializerContext from './deserializer';\nimport VanillaSerializerContext from './serializer';\nimport type { SyncParserContextOptions } from './sync';\nimport SyncParserContext from './sync';\n\nexport function serialize<T>(\n  source: T,\n  options: SyncParserContextOptions = {},\n): string {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new SyncParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n  });\n  const tree = ctx.parseTop(source);\n  const serial = new VanillaSerializerContext({\n    plugins,\n    features: ctx.features,\n    markedRefs: ctx.marked,\n  });\n  return serial.serializeTop(tree);\n}\n\nexport async function serializeAsync<T>(\n  source: T,\n  options: AsyncParserContextOptions = {},\n): Promise<string> {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new AsyncParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n  });\n  const tree = await ctx.parseTop(source);\n  const serial = new VanillaSerializerContext({\n    plugins,\n    features: ctx.features,\n    markedRefs: ctx.marked,\n  });\n  return serial.serializeTop(tree);\n}\n\nexport function deserialize<T>(source: string): T {\n  return (0, eval)(source) as T;\n}\n\nexport interface SerovalJSON {\n  t: SerovalNode;\n  f: number;\n  m: number[];\n}\n\nexport function toJSON<T>(\n  source: T,\n  options: SyncParserContextOptions = {},\n): SerovalJSON {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new SyncParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n  });\n  return {\n    t: ctx.parseTop(source),\n    f: ctx.features,\n    m: Array.from(ctx.marked),\n  };\n}\n\nexport async function toJSONAsync<T>(\n  source: T,\n  options: AsyncParserContextOptions = {},\n): Promise<SerovalJSON> {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new AsyncParserContext({\n    plugins,\n    disabledFeatures: options.disabledFeatures,\n  });\n  return {\n    t: await ctx.parseTop(source),\n    f: ctx.features,\n    m: Array.from(ctx.marked),\n  };\n}\n\nexport function compileJSON(\n  source: SerovalJSON,\n  options: PluginAccessOptions = {},\n): string {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new VanillaSerializerContext({\n    plugins,\n    features: source.f,\n    markedRefs: source.m,\n  });\n  return ctx.serializeTop(source.t);\n}\n\nexport function fromJSON<T>(\n  source: SerovalJSON,\n  options: PluginAccessOptions = {},\n): T {\n  const plugins = resolvePlugins(options.plugins);\n  const ctx = new VanillaDeserializerContext({\n    plugins,\n    markedRefs: source.m,\n  });\n  return ctx.deserializeTop(source.t) as T;\n}\n", "import { crossSerializeStream } from './cross';\nimport {\n  resolvePlugins,\n  type Plugin,\n  type PluginAccessOptions,\n} from './plugin';\nimport { serializeString } from './string';\n\nexport interface SerializerOptions extends PluginAccessOptions {\n  globalIdentifier: string;\n  scopeId?: string;\n  disabledFeatures?: number;\n  onData: (result: string) => void;\n  onError: (error: unknown) => void;\n  onDone?: () => void;\n}\n\nexport default class Serializer {\n  private alive = true;\n\n  private flushed = false;\n\n  private done = false;\n\n  private pending = 0;\n\n  private cleanups: (() => void)[] = [];\n\n  private refs = new Map<unknown, number>();\n\n  private plugins?: Plugin<any, any>[];\n\n  constructor(private options: SerializerOptions) {\n    this.plugins = resolvePlugins(options.plugins);\n  }\n\n  keys = new Set<string>();\n\n  write(key: string, value: unknown): void {\n    if (this.alive && !this.flushed) {\n      this.pending++;\n      this.keys.add(key);\n      this.cleanups.push(\n        crossSerializeStream(value, {\n          plugins: this.plugins,\n          scopeId: this.options.scopeId,\n          refs: this.refs,\n          disabledFeatures: this.options.disabledFeatures,\n          onError: this.options.onError,\n          onSerialize: (data, initial) => {\n            if (this.alive) {\n              this.options.onData(\n                initial\n                  ? this.options.globalIdentifier +\n                      '[\"' +\n                      serializeString(key) +\n                      '\"]=' +\n                      data\n                  : data,\n              );\n            }\n          },\n          onDone: () => {\n            if (this.alive) {\n              this.pending--;\n              if (\n                this.pending <= 0 &&\n                this.flushed &&\n                !this.done &&\n                this.options.onDone\n              ) {\n                this.options.onDone();\n                this.done = true;\n              }\n            }\n          },\n        }),\n      );\n    }\n  }\n\n  ids = 0;\n\n  private getNextID(): string {\n    while (this.keys.has('' + this.ids)) {\n      this.ids++;\n    }\n    return '' + this.ids;\n  }\n\n  push(value: unknown): string {\n    const newID = this.getNextID();\n    this.write(newID, value);\n    return newID;\n  }\n\n  flush(): void {\n    if (this.alive) {\n      this.flushed = true;\n      if (this.pending <= 0 && !this.done && this.options.onDone) {\n        this.options.onDone();\n        this.done = true;\n      }\n    }\n  }\n\n  close(): void {\n    if (this.alive) {\n      for (let i = 0, len = this.cleanups.length; i < len; i++) {\n        this.cleanups[i]();\n      }\n      if (!this.done && this.options.onDone) {\n        this.options.onDone();\n        this.done = true;\n      }\n      this.alive = false;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACMO,IAAW,UAAX,kBAAWA,aAAX;AACL,EAAAA,kBAAA,oBAAiB,KAAjB;AACA,EAAAA,kBAAA,mBAAgB,KAAhB;AACA,EAAAA,kBAAA,yBAAsB,KAAtB;AACA,EAAAA,kBAAA,kBAAe,KAAf;AACA,EAAAA,kBAAA,sBAAmB,MAAnB;AALgB,SAAAA;AAAA,GAAA;AAQX,IAAM,cACX,yBACA,wBACA,8BACA,uBACA;;;ACnBK,SAAS,cAAc,KAAiC;AAC7D,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAOO,SAAS,gBAAgB,KAAqB;AACnD,MAAI,SAAS;AACb,MAAI,UAAU;AACd,MAAI;AACJ,WAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,kBAAc,cAAc,IAAI,CAAC,CAAC;AAClC,QAAI,aAAa;AACf,gBAAU,IAAI,MAAM,SAAS,CAAC,IAAI;AAClC,gBAAU,IAAI;AAAA,IAChB;AAAA,EACF;AACA,MAAI,YAAY,GAAG;AACjB,aAAS;AAAA,EACX,OAAO;AACL,cAAU,IAAI,MAAM,OAAO;AAAA,EAC7B;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,KAAqB;AAChD,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEO,SAAS,kBAAkB,KAAqB;AACrD,SAAO,IAAI;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;;;AChFO,IAAM,iBAAiB;AAEvB,IAAM,4BAA4B;AAEzC,IAAM,mBAAmB,QAAQ,yBAAyB;AAEnD,SAAS,wBAAwB,IAAqB;AAC3D,MAAI,MAAM,MAAM;AACd,WAAO,GAAG,gBAAgB,IAAI,gBAAgB;AAAA,EAChD;AACA,SAAO,IAAI,gBAAgB,IAAI,gBAAgB,UAAU;AAAA,IACvD;AAAA,EACF,CAAC;AACH;;;AChBe,SAAR,OAAwB,MAAe,OAA4B;AACxE,MAAI,CAAC,MAAM;AACT,UAAM;AAAA,EACR;AACF;;;ACGA,IAAM,YAAY,oBAAI,IAAqB;AAC3C,IAAM,gBAAgB,oBAAI,IAAqB;AAExC,SAAS,gBAAmB,IAAY,OAAa;AAC1D,YAAU,IAAI,OAAO,EAAE;AACvB,gBAAc,IAAI,IAAI,KAAK;AAC3B,SAAO;AACT;AAEO,SAAS,eAAkB,OAAmB;AACnD,SAAO,UAAU,IAAI,KAAK;AAC5B;AAEO,SAAS,aAAa,IAAqB;AAChD,SAAO,cAAc,IAAI,EAAE;AAC7B;AAEO,SAAS,eAAkB,OAAkB;AAClD,SAAO,eAAe,KAAK,GAAG,IAAI,6BAA6B,KAAK,CAAC;AACrE,SAAO,UAAU,IAAI,KAAK;AAC5B;AAEO,SAAS,aAAgB,IAAe;AAC7C,SAAO,aAAa,EAAE,GAAG,IAAI,kCAAkC,EAAE,CAAC;AAClE,SAAO,cAAc,IAAI,EAAE;AAC7B;AAEA,IAAI,OAAO,eAAe,aAAa;AACrC,SAAO,eAAe,YAAY,gBAAgB;AAAA,IAChD,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AACH,WAAW,OAAO,WAAW,aAAa;AACxC,SAAO,eAAe,QAAQ,gBAAgB;AAAA,IAC5C,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AACH,WAAW,OAAO,SAAS,aAAa;AACtC,SAAO,eAAe,MAAM,gBAAgB;AAAA,IAC1C,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AACH,WAAW,OAAO,WAAW,aAAa;AACxC,SAAO,eAAe,QAAQ,gBAAgB;AAAA,IAC5C,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AACH;;;ACNO,SAAS,aACd,QACqB;AACrB,SAAO;AACT;AAMA,SAAS,cACP,SACA,SACM;AACN,WAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,UAAM,UAAU,QAAQ,CAAC;AACzB,QAAI,CAAC,QAAQ,IAAI,OAAO,GAAG;AACzB,cAAQ,IAAI,OAAO;AACnB,UAAI,QAAQ,SAAS;AACnB,sBAAc,SAAS,QAAQ,OAAO;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,eACd,SACgC;AAChC,MAAI,SAAS;AACX,UAAM,UAAU,oBAAI,IAAsB;AAC1C,kBAAc,SAAS,OAAO;AAC9B,WAAO,CAAC,GAAG,OAAO;AAAA,EACpB;AACA,SAAO;AACT;;;AClBO,IAAM,gBAAyC;AAAA,EACpD,CAAC,qBAAqB,GAAG;AAAA,EACzB,CAAC,mBAAmB,GAAG;AAAA,EACvB,CAAC,0BAA0B,GAAG;AAAA,EAC9B,CAAC,gBAAgB,GAAG;AAAA,EACpB,CAAC,aAAa,GAAG;AAAA,EACjB,CAAC,gBAAgB,GAAG;AAAA,EACpB,CAAC,eAAe,GAAG;AAAA,EACnB,CAAC,cAAc,GAAG;AAAA,EAClB,CAAC,eAAe,GAAG;AAAA,EACnB,CAAC,aAAa,GAAG;AAAA,EACjB,CAAC,oBAAmB,GAAG;AAAA,EACvB,CAAC,oBAAmB,GAAG;AAAA,EACvB,CAAC,oBAAmB,GAAG;AACzB;AAEO,IAAM,iBAAiC;AAAA,EAC5C,CAAC,OAAO,aAAa,GAAG;AAAA,EACxB,CAAC,OAAO,WAAW,GAAG;AAAA,EACtB,CAAC,OAAO,kBAAkB,GAAG;AAAA,EAC7B,CAAC,OAAO,QAAQ,GAAG;AAAA,EACnB,CAAC,OAAO,KAAK,GAAG;AAAA,EAChB,CAAC,OAAO,QAAQ,GAAG;AAAA,EACnB,CAAC,OAAO,OAAO,GAAG;AAAA,EAClB,CAAC,OAAO,MAAM,GAAG;AAAA,EACjB,CAAC,OAAO,OAAO,GAAG;AAAA,EAClB,CAAC,OAAO,KAAK,GAAG;AAAA,EAChB,CAAC,OAAO,WAAW,GAAG;AAAA,EACtB,CAAC,OAAO,WAAW,GAAG;AAAA,EACtB,CAAC,OAAO,WAAW,GAAG;AACxB;AAIO,IAAM,aAAgD;AAAA,EAC3D,CAAC,qBAAqB,GAAG,OAAO;AAAA,EAChC,CAAC,mBAAmB,GAAG,OAAO;AAAA,EAC9B,CAAC,0BAA0B,GAAG,OAAO;AAAA,EACrC,CAAC,gBAAgB,GAAG,OAAO;AAAA,EAC3B,CAAC,aAAa,GAAG,OAAO;AAAA,EACxB,CAAC,gBAAgB,GAAG,OAAO;AAAA,EAC3B,CAAC,eAAe,GAAG,OAAO;AAAA,EAC1B,CAAC,cAAc,GAAG,OAAO;AAAA,EACzB,CAAC,eAAe,GAAG,OAAO;AAAA,EAC1B,CAAC,aAAa,GAAG,OAAO;AAAA,EACxB,CAAC,oBAAmB,GAAG,OAAO;AAAA,EAC9B,CAAC,oBAAmB,GAAG,OAAO;AAAA,EAC9B,CAAC,oBAAmB,GAAG,OAAO;AAChC;AAEO,IAAM,kBAAmD;AAAA,EAC9D,CAAC,YAAoB,GAAG;AAAA,EACxB,CAAC,aAAqB,GAAG;AAAA,EACzB,CAAC,iBAAyB,GAAG;AAAA,EAC7B,CAAC,YAAoB,GAAG;AAAA,EACxB,CAAC,eAAuB,GAAG;AAAA,EAC3B,CAAC,WAAmB,GAAG;AAAA,EACvB,CAAC,cAAsB,GAAG;AAAA,EAC1B,CAAC,WAAmB,GAAG;AACzB;AAEO,IAAM,eAAiD;AAAA,EAC5D,CAAC,YAAoB,GAAG;AAAA,EACxB,CAAC,aAAqB,GAAG;AAAA,EACzB,CAAC,iBAAyB,GAAG;AAAA,EAC7B,CAAC,YAAoB,GAAG;AAAA,EACxB,CAAC,eAAuB,GAAG;AAAA,EAC3B,CAAC,WAAmB,GAAG,OAAO;AAAA,EAC9B,CAAC,cAAsB,GAAG,OAAO;AAAA,EACjC,CAAC,WAAmB,GAAG,OAAO;AAChC;AAYO,IAAM,2BAAgE;AAAA,EAC3E,CAAC,aAAyB,GAAG;AAAA,EAC7B,CAAC,iBAA6B,GAAG;AAAA,EACjC,CAAC,kBAA8B,GAAG;AAAA,EAClC,CAAC,sBAAkC,GAAG;AAAA,EACtC,CAAC,mBAA+B,GAAG;AAAA,EACnC,CAAC,iBAA6B,GAAG;AAAA,EACjC,CAAC,gBAA4B,GAAG;AAClC;AAWO,IAAM,oBACX;AAAA,EACE,CAAC,aAAyB,GAAG;AAAA,EAC7B,CAAC,iBAA6B,GAAG;AAAA,EACjC,CAAC,kBAA8B,GAAG;AAAA,EAClC,CAAC,sBAAkC,GAAG;AAAA,EACtC,CAAC,mBAA+B,GAAG;AAAA,EACnC,CAAC,iBAA6B,GAAG;AAAA,EACjC,CAAC,gBAA4B,GAAG;AAClC;AAEK,IAAM,MAAM;;;AChLZ,SAAS,kBAId,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACA,GACG;AACH,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACnCA,SAAS,mBAAmB,OAA6C;AACvE,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,IAAM,YAA4B;AAAA;AAEzC;AACO,IAAM,aAA6B;AAAA;AAE1C;AACO,IAAM,iBAAiC;AAAA;AAE9C;AACO,IAAM,YAA4B;AAAA;AAEzC;AACO,IAAM,gBAAgC;AAAA;AAE7C;AACO,IAAM,gBAAgC;AAAA;AAE7C;AACO,IAAM,oBAAoC;AAAA;AAEjD;AACO,IAAM,WAA2B,8CAAsC;;;AC7BvE,SAAS,oBAAoB,OAAwC;AAC1E,MAAI,iBAAiB,WAAW;AAC9B;AAAA,EACF;AACA,MAAI,iBAAiB,YAAY;AAC/B;AAAA,EACF;AACA,MAAI,iBAAiB,gBAAgB;AACnC;AAAA,EACF;AACA,MAAI,iBAAiB,aAAa;AAChC;AAAA,EACF;AACA,MAAI,iBAAiB,WAAW;AAC9B;AAAA,EACF;AACA,MAAI,iBAAiB,UAAU;AAC7B;AAAA,EACF;AACA;AACF;AAEA,SAAS,uBACP,OACqC;AACrC,QAAM,YAAY,yBAAyB,oBAAoB,KAAK,CAAC;AAErE,MAAI,MAAM,SAAS,WAAW;AAC5B,WAAO,EAAE,MAAM,MAAM,KAAK;AAAA,EAC5B;AACA,MAAI,MAAM,YAAY,SAAS,WAAW;AAGxC,WAAO,EAAE,MAAM,MAAM,YAAY,KAAK;AAAA,EACxC;AACA,SAAO,CAAC;AACV;AAEO,SAAS,gBACd,OACA,UACqC;AACrC,MAAI,UAAU,uBAAuB,KAAK;AAC1C,QAAM,QAAQ,OAAO,oBAAoB,KAAK;AAC9C,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,MAAc,IAAI,KAAK,KAAK;AAC9D,WAAO,MAAM,CAAC;AACd,QAAI,SAAS,UAAU,SAAS,WAAW;AACzC,UAAI,SAAS,SAAS;AACpB,YAAI,wCAAwC;AAC1C,oBAAU,WAAW,CAAC;AACtB,kBAAQ,IAAI,IAAI,MAAM,IAAmB;AAAA,QAC3C;AAAA,MACF,OAAO;AACL,kBAAU,WAAW,CAAC;AACtB,gBAAQ,IAAI,IAAI,MAAM,IAAmB;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACtEO,SAAS,cAAc,KAAkC;AAC9D,MAAI,OAAO,SAAS,GAAG,GAAG;AACxB;AAAA,EACF;AACA,MAAI,OAAO,SAAS,GAAG,GAAG;AACxB;AAAA,EACF;AACA,MAAI,OAAO,aAAa,GAAG,GAAG;AAC5B;AAAA,EACF;AACA;AACF;;;ACmCO,SAAS,iBACd,OACyC;AACzC,UAAQ,OAAO;AAAA,IACb,KAAK,OAAO;AACV,aAAO;AAAA,IACT,KAAK,OAAO;AACV,aAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO;AACnB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,GAAG,OAAO,EAAE,GAAG;AACxB,WAAO;AAAA,EACT;AACA,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBAAiB,OAAkC;AACjE,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,gBAAgB,KAAK;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBAAiB,SAAoC;AACnE,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,uBAAuB,IAAqC;AAC1E,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,eAAe,IAAY,SAAgC;AACzE,QAAM,YAAY,QAAQ,QAAQ;AAClC,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,cAAc,YAAY,KAAK,QAAQ,YAAY;AAAA,IACnD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBACd,IACA,SACmB;AACnB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,QAAQ,MAAM;AAAA,IAC9B,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,sBACd,IACA,SACwB;AACxB,QAAM,QAAQ,IAAI,WAAW,OAAO;AACpC,QAAM,MAAM,MAAM;AAClB,QAAM,SAAS,IAAI,MAAc,GAAG;AACpC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,WAAO,CAAC,IAAI,MAAM,CAAC;AAAA,EACrB;AACA,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,mBACd,IACA,SACqB;AACrB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,eAAe,OAAO;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,oBACd,IACA,KACsB;AACtB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,gBAAgB,eAAe,GAAG,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,iBACd,IACA,KACA,OACmB;AACnB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,GAAG;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,gBACd,IACA,SACA,aACkB;AAClB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,OAAO;AAAA,EACvB;AACF;AAEO,SAAS,gBACd,IACA,OACkB;AAClB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,qBACd,IACA,SACA,QACuB;AACvB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,YAAY;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,2BACd,IACA,SACA,QAC6B;AAC7B,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,YAAY;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,mBACd,IACA,SACA,QACqB;AACrB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,gBACd,IACA,SACA,SACkB;AAClB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,oBAAoB,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,gBAAgB,QAAQ,OAAO;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,yBACd,IACA,SACA,SAC2B;AAC3B,SAAO;AAAA;AAAA,IAEL;AAAA,IACA,oBAAoB,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,gBAAgB,QAAQ,OAAO;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,cACd,IACA,MACA,OACgB;AAChB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,kCACd,SACA,OACoC;AACpC,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,SAAS,KAAK;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,uCACd,SACA,OACyC;AACzC,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,SAAS,KAAK;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,4BACd,IACA,SACA,UAC8B;AAC9B,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,qBACd,IACA,QACuB;AACvB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,sBACd,IACA,QACwB;AACxB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,uBACd,IACA,QACyB;AACzB,SAAO;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC1hBA,IAAM,EAAE,UAAU,eAAe,IAAoB,OAAO;AAE5D,SAAS,gBAAgB,MAAc,OAAoB;AACzD,MAAI,iBAAiB,OAAO;AAC1B,WAAO,sCAAsC,IAAI;AAAA;AAAA,EAEnD,MAAM,IAAI;AAAA,EACV,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAIb;AACA,SAAO,sCAAsC,IAAI;AAAA;AAAA,GAEhD,eAAe,KAAK,KAAK,CAAC;AAAA;AAAA;AAG7B;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAA,EACtC,YACE,MACO,OACP;AACA,UAAM,gBAAgB,MAAM,KAAK,CAAC;AAF3B;AAAA,EAGT;AACF;AAEO,IAAM,qBAAN,cAAiC,aAAa;AAAA,EACnD,YAAY,OAAY;AACtB,UAAM,WAAW,KAAK;AAAA,EACxB;AACF;AAEO,IAAM,4BAAN,cAAwC,aAAa;AAAA,EAC1D,YAAY,OAAY;AACtB,UAAM,iBAAiB,KAAK;AAAA,EAC9B;AACF;AAEO,IAAM,8BAAN,cAA0C,aAAa;AAAA,EAC5D,YAAY,OAAY;AACtB,UAAM,mBAAmB,KAAK;AAAA,EAChC;AACF;AAEO,IAAM,8BAAN,cAA0C,MAAM;AAAA,EACrD,YAAmB,OAAgB;AACjC;AAAA,MACE,aAAa,eAAe,KAAK,KAAK,CAAC,aAAa,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,IAKlE;AAPiB;AAAA,EAQnB;AACF;AAEO,IAAM,8BAAN,cAA0C,MAAM;AAAA,EACrD,YAAY,MAAmB;AAC7B,UAAM,4BAA4B,KAAK,IAAI,IAAI;AAAA,EACjD;AACF;AAEO,IAAM,4BAAN,cAAwC,MAAM;AAAA,EACnD,YAAY,KAAa;AACvB,UAAM,6BAA6B,MAAM,IAAI;AAAA,EAC/C;AACF;AAEO,IAAM,8BAAN,cAA0C,MAAM;AAAA,EACrD,YAAY,KAAa;AACvB,UAAM,cAAc,MAAM,aAAa;AAAA,EACzC;AACF;AAEO,IAAM,+BAAN,cAA2C,MAAM;AAAA,EACtD,YAAmB,OAAgB;AACjC;AAAA,MACE,sCACE,eAAe,KAAK,KAAK,IACzB,gBACA,OAAO,QACP;AAAA,IACJ;AAPiB;AAAA,EAQnB;AACF;AAEO,IAAM,oCAAN,cAAgD,MAAM;AAAA,EAC3D,YAAY,IAAY;AACtB,UAAM,+BAA+B,gBAAgB,EAAE,IAAI,GAAG;AAAA,EAChE;AACF;AAEO,IAAM,gCAAN,cAA4C,MAAM;AAAA,EACvD,YAAY,MAAc;AACxB,UAAM,yBAAyB,OAAO,GAAG;AAAA,EAC3C;AACF;;;AClGO,IAAM,kBAAN,MAAwC;AAAA,EAC7C,YACkB,OACA,aAChB;AAFgB;AACA;AAAA,EACf;AACL;;;ACNO,SAAS,eACd,UACA,YACA,MACQ;AACR,MAAI,kCAAkC;AACpC,UAAM,SACJ,WAAW,WAAW,IAClB,WAAW,CAAC,IACZ,MAAM,WAAW,KAAK,GAAG,IAAI;AACnC,WAAO,SAAS,QAAQ,KAAK,WAAW,GAAG,IAAI,MAAM,OAAO,MAAM;AAAA,EACpE;AACA,SAAO,cAAc,WAAW,KAAK,GAAG,IAAI,cAAc,OAAO;AACnE;AAEO,SAAS,wBACd,UACA,YACA,MACQ;AACR,MAAI,kCAAkC;AACpC,UAAM,SACJ,WAAW,WAAW,IAClB,WAAW,CAAC,IACZ,MAAM,WAAW,KAAK,GAAG,IAAI;AACnC,WAAO,SAAS,QAAQ,OAAO;AAAA,EACjC;AACA,SAAO,cAAc,WAAW,KAAK,GAAG,IAAI,OAAO,OAAO;AAC5D;;;AC5BO,IAAM,WAAW,CAAC;AAElB,IAAM,iBAAiB,CAAC;AAaxB,IAAM,eAAkD;AAAA,EAC7D,CAAC,mBAA4B,GAAG,CAAC;AAAA,EACjC,CAAC,0BAAmC,GAAG,CAAC;AAAA,EACxC,CAAC,sBAA+B,GAAG,CAAC;AAAA,EACpC,CAAC,sBAA+B,GAAG,CAAC;AAAA,EACpC,CAAC,yBAAkC,GAAG,CAAC;AACzC;AAEA,SAAS,4BAA4B,UAA0B;AAC7D,SAAO;AAAA,IACL;AAAA,IACA,CAAC,GAAG;AAAA,IACJ,sBACE,wBAAwB,UAAU,CAAC,KAAK,GAAG,GAAG,aAAa,IAC3D;AAAA,EACJ;AACF;AAEA,SAAS,wBAAwB,UAA0B;AACzD,SAAO;AAAA,IACL;AAAA,IACA,CAAC,KAAK,GAAG;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,UAA0B;AACzD,SAAO;AAAA,IACL;AAAA,IACA,CAAC,KAAK,GAAG;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,2BAA2B,UAA0B;AAC5D,SAAO;AAAA,IACL;AAAA,IACA,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IACvC,gCACE;AAAA,MACE;AAAA,MACA,CAAC,KAAK,KAAK,GAAG;AAAA,MACd;AAAA,IACF,IACA,QACA;AAAA,MACE;AAAA,MACA,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF,IACA,QACA;AAAA,MACE;AAAA,MACA,CAAC,KAAK,GAAG;AAAA,MACT,2BACE,wBAAwB,UAAU,CAAC,GAAG,kBAAkB,IACxD;AAAA,IACJ,IACA,gCACA,eAAe,UAAU,CAAC,GAAG,GAAG,MAAM,IACtC,WACA,wBAAwB,UAAU,CAAC,GAAG,GAAG,4BAA4B,IACrE,YACA;AAAA,MACE;AAAA,MACA,CAAC,GAAG;AAAA,MACJ;AAAA,IACF,IACA,aACA;AAAA,MACE;AAAA,MACA,CAAC,GAAG;AAAA,MACJ;AAAA,IACF,IACA;AAAA,EACJ;AACF;AAEO,SAAS,+BACd,UACA,KACQ;AACR,UAAQ,KAAK;AAAA,IACX,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,4BAA4B,QAAQ;AAAA,IAC7C,KAAK;AACH,aAAO,wBAAwB,QAAQ;AAAA,IACzC,KAAK;AACH,aAAO,wBAAwB,QAAQ;AAAA,IACzC,KAAK;AACH,aAAO,2BAA2B,QAAQ;AAAA,IAC5C;AACE,aAAO;AAAA,EACX;AACF;;;AC3GO,SAAS,iBAA2B;AACzC,MAAI;AACJ,MAAI;AACJ,SAAO;AAAA,IACL,SAAS,IAAI,QAAiB,CAAC,KAAK,QAAQ;AAC1C,gBAAU;AACV,eAAS;AAAA,IACX,CAAC;AAAA,IACD,QAAQ,OAAa;AACnB,cAAQ,KAAK;AAAA,IACf;AAAA,IACA,OAAO,OAAa;AAClB,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACF;;;ACFO,SAAS,SAAY,OAAmC;AAC7D,SAAO,wBAAwB;AACjC;AAEO,SAAS,eAA6B;AAC3C,QAAM,YAAY,oBAAI,IAAuB;AAC7C,QAAM,SAAoB,CAAC;AAC3B,MAAI,QAAQ;AACZ,MAAI,UAAU;AAEd,WAAS,UAAU,OAAgB;AACjC,eAAW,YAAY,UAAU,KAAK,GAAG;AACvC,eAAS,KAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAEA,WAAS,WAAW,OAAsB;AACxC,eAAW,YAAY,UAAU,KAAK,GAAG;AACvC,eAAS,MAAM,KAAK;AAAA,IACtB;AAAA,EACF;AAEA,WAAS,YAAY,OAAgB;AACnC,eAAW,YAAY,UAAU,KAAK,GAAG;AACvC,eAAS,OAAO,KAAK;AAAA,IACvB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,oBAAoB;AAAA,IACpB,GAAG,UAAyC;AAC1C,UAAI,OAAO;AACT,kBAAU,IAAI,QAAQ;AAAA,MACxB;AACA,eAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,cAAM,QAAQ,OAAO,CAAC;AACtB,YAAI,MAAM,MAAM,KAAK,CAAC,OAAO;AAC3B,cAAI,SAAS;AACX,qBAAS,OAAO,KAAU;AAAA,UAC5B,OAAO;AACL,qBAAS,MAAM,KAAK;AAAA,UACtB;AAAA,QACF,OAAO;AACL,mBAAS,KAAK,KAAU;AAAA,QAC1B;AAAA,MACF;AACA,aAAO,MAAM;AACX,YAAI,OAAO;AACT,oBAAU,OAAO,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,IACA,KAAK,OAAa;AAChB,UAAI,OAAO;AACT,eAAO,KAAK,KAAK;AACjB,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,IACA,MAAM,OAAa;AACjB,UAAI,OAAO;AACT,eAAO,KAAK,KAAK;AACjB,mBAAW,KAAK;AAChB,gBAAQ;AACR,kBAAU;AACV,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAAA,IACA,OAAO,OAAa;AAClB,UAAI,OAAO;AACT,eAAO,KAAK,KAAK;AACjB,oBAAY,KAAK;AACjB,gBAAQ;AACR,kBAAU;AACV,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,8BACd,UACW;AACX,QAAM,SAAS,aAAgB;AAE/B,QAAM,WAAW,SAAS,OAAO,aAAa,EAAE;AAEhD,iBAAe,OAAsB;AACnC,QAAI;AACF,YAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,UAAI,MAAM,MAAM;AACd,eAAO,OAAO,MAAM,KAAU;AAAA,MAChC,OAAO;AACL,eAAO,KAAK,MAAM,KAAK;AACvB,cAAM,KAAK;AAAA,MACb;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,OAAK,EAAE,MAAM,MAAM;AAAA,EAEnB,CAAC;AAED,SAAO;AACT;AAEO,SAAS,sBACd,QACgC;AAChC,SAAO,MAAgC;AACrC,UAAM,SAAc,CAAC;AACrB,UAAM,UAAsB,CAAC;AAC7B,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,QAAI,UAAU;AAEd,aAAS,aAAmB;AAC1B,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,gBAAQ,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAM,OAAO,OAAU,CAAC;AAAA,MACrD;AAAA,IACF;AAEA,WAAO,GAAG;AAAA,MACR,KAAK,OAAO;AACV,cAAM,UAAU,QAAQ,MAAM;AAC9B,YAAI,SAAS;AACX,kBAAQ,QAAQ,EAAE,MAAM,OAAO,MAAM,CAAC;AAAA,QACxC;AACA,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,MACA,MAAM,OAAO;AACX,cAAM,UAAU,QAAQ,MAAM;AAC9B,YAAI,SAAS;AACX,kBAAQ,OAAO,KAAK;AAAA,QACtB;AACA,mBAAW;AACX,iBAAS,OAAO;AAChB,eAAO,KAAK,KAAU;AACtB,kBAAU;AAAA,MACZ;AAAA,MACA,OAAO,OAAO;AACZ,cAAM,UAAU,QAAQ,MAAM;AAC9B,YAAI,SAAS;AACX,kBAAQ,QAAQ,EAAE,MAAM,MAAM,MAAM,CAAC;AAAA,QACvC;AACA,mBAAW;AACX,iBAAS,OAAO;AAChB,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF,CAAC;AAED,aAAS,WAAW;AAClB,YAAM,UAAU;AAChB,YAAM,QAAQ,OAAO,OAAO;AAC5B,UAAI,YAAY,QAAQ;AACtB,eAAO,EAAE,MAAM,OAAO,MAAM;AAAA,MAC9B;AACA,UAAI,SAAS;AACX,cAAM;AAAA,MACR;AACA,aAAO,EAAE,MAAM,MAAM,MAAM;AAAA,IAC7B;AAEA,WAAO;AAAA,MACL,CAAC,OAAO,aAAa,IAA8B;AACjD,eAAO;AAAA,MACT;AAAA,MACA,MAAM,OAAmC;AACvC,YAAI,WAAW,IAAI;AACjB,gBAAM,UAAU;AAChB,cAAI,WAAW,OAAO,QAAQ;AAC5B,kBAAM,WAAW,eAAe;AAChC,oBAAQ,KAAK,QAAQ;AACrB,mBAAQ,MAAM,SAAS;AAAA,UACzB;AACA,iBAAO,EAAE,MAAM,OAAO,OAAO,OAAO,OAAO,EAAE;AAAA,QAC/C;AACA,YAAI,QAAQ,QAAQ;AAClB,iBAAO,EAAE,MAAM,MAAM,OAAO,OAAU;AAAA,QACxC;AACA,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;;;ACpMO,SAAS,mBAAsB,QAA+B;AACnE,QAAM,SAAoB,CAAC;AAC3B,MAAI,WAAW;AACf,MAAI,SAAS;AAEb,QAAM,WAAW,OAAO,OAAO,QAAQ,EAAE;AAEzC,SAAO,MAAM;AACX,QAAI;AACF,YAAM,QAAQ,SAAS,KAAK;AAC5B,aAAO,KAAK,MAAM,KAAK;AACvB,UAAI,MAAM,MAAM;AACd,iBAAS,OAAO,SAAS;AACzB;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,iBAAW,OAAO;AAClB,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEO,SAAS,mBACd,UAC2B;AAC3B,SAAO,MAA2B;AAChC,QAAI,QAAQ;AAEZ,WAAO;AAAA,MACL,CAAC,OAAO,QAAQ,IAAyB;AACvC,eAAO;AAAA,MACT;AAAA,MACA,OAA0B;AACxB,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,eAAe;AACrB,cAAM,cAAc,SAAS,EAAE,YAAY;AAC3C,YAAI,iBAAiB,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AACA,eAAO;AAAA,UACL,MAAM,iBAAiB,SAAS;AAAA,UAChC,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACjEA,eAAO,gBACL,SAC2B;AAC3B,MAAI;AACF,WAAO,CAAC,GAAG,MAAM,OAAO;AAAA,EAC1B,SAAS,GAAG;AACV,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AACF;;;ACuDO,IAAe,oBAAf,MAAgE;AAAA,EAWrE,YAAY,SAAmC;AAN/C,kBAAS,oBAAI,IAAY;AAOvB,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,eAAe,QAAQ,oBAAoB;AAC3D,SAAK,OAAO,QAAQ,QAAQ,oBAAI,IAAqB;AAAA,EACvD;AAAA,EAEU,QAAQ,IAAkB;AAClC,SAAK,OAAO,IAAI,EAAE;AAAA,EACpB;AAAA,EAEU,SAAS,IAAqB;AACtC,WAAO,KAAK,OAAO,IAAI,EAAE;AAAA,EAC3B;AAAA,EAEU,YAAe,SAAoB;AAC3C,UAAM,KAAK,KAAK,KAAK;AACrB,SAAK,KAAK,IAAI,SAAS,EAAE;AACzB,WAAO;AAAA,EACT;AAAA,EAEU,gBAAmB,SAAqC;AAChE,UAAM,eAAe,KAAK,KAAK,IAAI,OAAO;AAC1C,QAAI,gBAAgB,MAAM;AACxB,WAAK,QAAQ,YAAY;AACzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO,uBAAuB,YAAY;AAAA,MAC5C;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO,KAAK,YAAY,OAAO;AAAA,IACjC;AAAA,EACF;AAAA,EAEU,aAAgB,SAAwB;AAChD,UAAM,UAAU,KAAK,gBAAgB,OAAO;AAC5C,QAAI,QAAQ,SAAS,iBAAwB;AAC3C,aAAO;AAAA,IACT;AACA,QAAI,eAAe,OAAO,GAAG;AAC3B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO,oBAAoB,QAAQ,OAAO,OAAO;AAAA,MACnD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEU,qBACR,SACsE;AACtE,UAAM,MAAM,KAAK,aAAa,OAAO;AACrC,QAAI,IAAI,SAAS,eAAsB;AACrC,aAAO,IAAI;AAAA,IACb;AACA,WAAO,WAAW,gBAAgB,IAAI,4BAA4B,OAAO,CAAC;AAC1E,WAAO,mBAAmB,IAAI,OAAO,OAA2B;AAAA,EAClE;AAAA,EAEU,sBACR,KACuD;AACvD,UAAM,SAAS,KAAK,gBAAgB,aAAa,GAAG,CAAC;AACrD,QAAI,OAAO,SAAS,iBAAwB;AAC1C,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA;AAAA,MAEL,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEU,uBAEqB;AAC7B,UAAM,SAAS,KAAK,gBAAgB,QAAQ;AAC5C,QAAI,OAAO,SAAS,iBAAwB;AAC1C,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA;AAAA,MAEL,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,qBAAqB,OAAO,QAAQ;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEU,4BAE0B;AAClC,UAAM,SAAS,KAAK,gBAAgB,cAAc;AAClD,QAAI,OAAO,SAAS,iBAAwB;AAC1C,aAAO,OAAO;AAAA,IAChB;AACA,WAAO;AAAA;AAAA,MAEL,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,KAAK,gDAAyD;AAAA,QAC9D,KAAK,qBAAqB,OAAO,aAAa;AAAA,MAChD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEU,iBACR,IACA,SACA,OACA,QACgD;AAChD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,OAAO;AAAA,IACvB;AAAA,EACF;AAAA,EAEU,cACR,IACA,GACA,GACA,GACgB;AAChB,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,EAAE,GAAG,GAAG,EAAE;AAAA,MACV;AAAA,MACA,KAAK,yCAAkD;AAAA,MACvD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEU,6BACR,IACA,UAC+B;AAC/B,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,gDAAyD;AAAA,MAC9D;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;ACtMA,IAA8B,yBAA9B,cAA6D,kBAAkB;AAAA,EAC7E,MAAc,WAAW,SAA4C;AACnE,UAAM,QAAQ,CAAC;AACf,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAElD,UAAI,KAAK,SAAS;AAChB,cAAM,CAAC,IAAI,MAAM,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,WACZ,IACA,SAC2B;AAC3B,WAAO,gBAAgB,IAAI,SAAS,MAAM,KAAK,WAAW,OAAO,CAAC;AAAA,EACpE;AAAA,EAEA,MAAc,gBACZ,YACkC;AAClC,UAAM,UAAU,OAAO,QAAQ,UAAU;AACzC,UAAM,WAAqC,CAAC;AAC5C,UAAM,aAA4B,CAAC;AACnC,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,eAAS,KAAK,gBAAgB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,iBAAW,KAAK,MAAM,KAAK,MAAM,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IACjD;AAEA,QAAI,SAAS,OAAO;AACpB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,qBAAqB;AAAA,UAC1B,MAAM,KAAK;AAAA,YACT,mBAAmB,UAA0C;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,0BAA0B;AAAA,UAC/B,MAAM,KAAK;AAAA,YACT;AAAA,cACE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,iBAAiB,WAAW,MAAM,CAAW,CAAC;AAAA,IAChE;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,WAAW,MAAM,IAAI,YAAY,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA,EAEA,MAAc,iBACZ,IACA,SACA,OACyB;AACzB,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,gBAAgB,OAAO;AAAA,IACpC;AAAA,EACF;AAAA,EAEA,MAAc,WACZ,IACA,SAC2B;AAC3B,WAAO,gBAAgB,IAAI,MAAM,KAAK,MAAM,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAChE;AAAA,EAEA,MAAc,gBACZ,IACA,SACgC;AAChC,WAAO,qBAAqB,IAAI,SAAS,MAAM,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,EAC3E;AAAA,EAEA,MAAc,sBACZ,IACA,SACsC;AACtC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,KAAK,MAAM,QAAQ,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EAEA,MAAc,cACZ,IACA,SAC8B;AAC9B,WAAO,mBAAmB,IAAI,SAAS,MAAM,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,EACzE;AAAA,EAEA,MAAc,WACZ,IACA,SAC2B;AAC3B,UAAM,UAAU,gBAAgB,SAAS,KAAK,QAAQ;AACtD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU,MAAM,KAAK,gBAAgB,OAAO,IAAI;AAAA,IAClD;AAAA,EACF;AAAA,EAEA,MAAc,oBACZ,IACA,SACoC;AACpC,UAAM,UAAU,gBAAgB,SAAS,KAAK,QAAQ;AACtD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU,MAAM,KAAK,gBAAgB,OAAO,IAAI;AAAA,IAClD;AAAA,EACF;AAAA,EAEA,MAAc,SACZ,IACA,SACyB;AACzB,UAAM,WAA0B,CAAC;AACjC,UAAM,aAA4B,CAAC;AACnC,eAAW,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC5C,eAAS,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC;AACnC,iBAAW,KAAK,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,IACzC;AACA,WAAO,KAAK,cAAc,IAAI,UAAU,YAAY,QAAQ,IAAI;AAAA,EAClE;AAAA,EAEA,MAAc,SACZ,IACA,SACyB;AACzB,UAAM,QAAuB,CAAC;AAC9B,eAAW,QAAQ,QAAQ,KAAK,GAAG;AACjC,YAAM,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;AAAA,IACnC;AACA,WAAO,cAAc,IAAI,QAAQ,MAAM,KAAK;AAAA,EAC9C;AAAA,EAEA,MAAc,aACZ,IACA,SAC6B;AAC7B,UAAM,CAAC,QAAQ,MAAM,IAAI,MAAM,gBAAgB,OAAO;AAEtD,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,MAAM,MAAM;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAc,YACZ,IACA,SACwC;AACxC,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,eAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,cAAM,SAAS,eAAe,CAAC;AAC/B,YAAI,OAAO,MAAM,SAAS,OAAO,KAAK,OAAO,GAAG;AAC9C,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,YACP,MAAM,OAAO,MAAM,MAAM,SAAS,MAAM;AAAA,cACtC;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAc,YACZ,IACA,SACuC;AACvC,WAAO;AAAA,MACL;AAAA,MACA,KAAK,+CAAwD;AAAA,MAC7D,MAAM,IAAI,QAAuB,CAAC,SAAS,WAAW;AACpD,cAAM,WAA0B,CAAC;AACjC,cAAM,UAAU,QAAQ,GAAG;AAAA,UACzB,MAAM,WAAS;AACb,iBAAK,QAAQ,EAAE;AACf,iBAAK,MAAM,KAAK,EAAE;AAAA,cAChB,UAAQ;AACN,yBAAS,KAAK,qBAAqB,IAAI,IAAI,CAAC;AAAA,cAC9C;AAAA,cACA,UAAQ;AACN,uBAAO,IAAI;AACX,wBAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,OAAO,WAAS;AACd,iBAAK,QAAQ,EAAE;AACf,iBAAK,MAAM,KAAK,EAAE;AAAA,cAChB,UAAQ;AACN,yBAAS,KAAK,sBAAsB,IAAI,IAAI,CAAC;AAC7C,wBAAQ,QAAQ;AAChB,wBAAQ;AAAA,cACV;AAAA,cACA,UAAQ;AACN,uBAAO,IAAI;AACX,wBAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ,WAAS;AACf,iBAAK,QAAQ,EAAE;AACf,iBAAK,MAAM,KAAK,EAAE;AAAA,cAChB,UAAQ;AACN,yBAAS,KAAK,uBAAuB,IAAI,IAAI,CAAC;AAC9C,wBAAQ,QAAQ;AAChB,wBAAQ;AAAA,cACV;AAAA,cACA,UAAQ;AACN,uBAAO,IAAI;AACX,wBAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,MAAc,YAAY,IAAY,SAAuC;AAC3E,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,aAAO,KAAK,WAAW,IAAI,OAAO;AAAA,IACpC;AACA,QAAI,SAAS,OAAO,GAAG;AACrB,aAAO,KAAK,YAAY,IAAI,OAAO;AAAA,IACrC;AACA,UAAM,eAAe,QAAQ;AAC7B,QAAI,iBAAiB,iBAAiB;AACpC,aAAO,KAAK;AAAA,QACT,QAA8C;AAAA,MACjD;AAAA,IACF;AACA,UAAM,SAAS,MAAM,KAAK,YAAY,IAAI,OAAO;AACjD,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,eAAO,KAAK;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,KAAK;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,eAAe,IAAI,OAA0B;AAAA,MACtD,KAAK;AACH,eAAO,iBAAiB,IAAI,OAA4B;AAAA,MAC1D,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,WAAW,IAAI,OAA2B;AAAA,MACxD,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,WAAW,IAAI,OAAO;AAAA,MACpC,KAAK;AACH,eAAO,sBAAsB,IAAI,OAAiC;AAAA,MACpE,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,gBAAgB,IAAI,OAAqC;AAAA,MACvE,KAAK;AACH,eAAO,KAAK,cAAc,IAAI,OAA8B;AAAA,MAC9D,KAAK;AACH,eAAO,KAAK,SAAS,IAAI,OAA2C;AAAA,MACtE,KAAK;AACH,eAAO,KAAK,SAAS,IAAI,OAAkC;AAAA,MAC7D;AACE;AAAA,IACJ;AAEA,QAAI,iBAAiB,WAAW,mBAAmB,SAAS;AAC1D,aAAO,KAAK,aAAa,IAAI,OAAsC;AAAA,IACrE;AACA,UAAM,kBAAkB,KAAK;AAE7B,QAAI,6CAA4C;AAC9C,cAAQ,cAAc;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK;AAAA,YACV;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF;AACA,QACE,4CACA,OAAO,mBAAmB,gBACzB,iBAAiB,kBAAkB,mBAAmB,iBACvD;AACA,aAAO,KAAK,oBAAoB,IAAI,OAAoC;AAAA,IAC1E;AAGA,QAAI,mBAAmB,OAAO;AAC5B,aAAO,KAAK,WAAW,IAAI,OAAO;AAAA,IACpC;AAGA,QAAI,OAAO,YAAY,WAAW,OAAO,iBAAiB,SAAS;AACjE,aAAO,KAAK,iBAAiB,IAAI,SAAS,CAAC,CAAC,YAAY;AAAA,IAC1D;AACA,UAAM,IAAI,4BAA4B,OAAO;AAAA,EAC/C;AAAA,EAEA,MAAgB,cAAc,SAAwC;AACpE,UAAM,MAAM,KAAK,aAAa,OAAO;AACrC,QAAI,IAAI,wBAA+B;AACrC,aAAO,IAAI;AAAA,IACb;AACA,UAAM,SAAS,MAAM,KAAK,YAAY,IAAI,OAAO,OAAO;AACxD,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,IAAI,4BAA4B,OAAO;AAAA,EAC/C;AAAA,EAEA,MAAM,MAAS,SAAkC;AAC/C,YAAQ,OAAO,SAAS;AAAA,MACtB,KAAK;AACH,eAAO,UAAU,YAAY;AAAA,MAC/B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK,UAAU;AACb,YAAI,SAAS;AACX,gBAAM,MAAM,KAAK,aAAa,OAAO;AACrC,iBAAO,IAAI,SAAS,IAChB,MAAM,KAAK,YAAY,IAAI,OAAO,OAAiB,IACnD,IAAI;AAAA,QACV;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AACH,eAAO,KAAK,qBAAqB,OAAO;AAAA,MAC1C,KAAK;AACH,eAAO,KAAK,cAAc,OAAO;AAAA,MACnC;AACE,cAAM,IAAI,4BAA4B,OAAO;AAAA,IACjD;AAAA,EACF;AAAA,EAEA,MAAM,SAAY,SAAkC;AAClD,QAAI;AACF,aAAO,MAAM,KAAK,MAAM,OAAO;AAAA,IACjC,SAAS,OAAO;AACd,YAAM,iBAAiB,qBACnB,QACA,IAAI,mBAAmB,KAAK;AAAA,IAClC;AAAA,EACF;AACF;;;ACxeA,IAAqB,0BAArB,cAAqD,uBAAuB;AAAA,EAA5E;AAAA;AACE,SAAS,OAAoB;AAAA;AAC/B;;;ACoBO,SAAS,yBAAyB,MAAqC;AAC5E,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,YAAM,IAAI,8BAA8B,IAAI;AAAA,EAChD;AACF;;;ACOA,SAAS,gBAAgB,KAAc,MAAmC;AACxE,UAAQ,MAAM;AAAA,IACZ;AACE,aAAO,OAAO,OAAO,GAAG;AAAA,IAC1B;AACE,aAAO,OAAO,kBAAkB,GAAG;AAAA,IACrC;AACE,aAAO,OAAO,KAAK,GAAG;AAAA,IACxB;AACE,aAAO;AAAA,EACX;AACF;AASA,IAA8B,0BAA9B,MAEA;AAAA,EAWE,YAAY,SAAkC;AAC5C,SAAK,UAAU,QAAQ;AACvB,SAAK,OAAO,QAAQ,QAAQ,oBAAI,IAAqB;AAAA,EACvD;AAAA,EAIQ,qBAAqB,MAAqC;AAChE,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,aAAa,kBAAkB,KAAK,CAAC,CAAC;AAAA,IACxC;AAAA,EACF;AAAA,EAEQ,iBAAiB,MAAmC;AAC1D,UAAM,MAAM,KAAK;AACjB,UAAM,SAAoB,KAAK;AAAA,MAC7B,KAAK;AAAA,MACL,IAAI,MAAe,GAAG;AAAA,IACxB;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAO,KAAK,EAAE,CAAC;AACf,UAAI,MAAM;AACR,eAAO,CAAC,IAAI,KAAK,YAAY,IAAI;AAAA,MACnC;AAAA,IACF;AACA,oBAAgB,QAAQ,KAAK,CAAC;AAC9B,WAAO;AAAA,EACT;AAAA,EAEQ,sBACN,MACA,QACkC;AAClC,UAAM,MAAM,KAAK;AACjB,QAAI,KAAK;AACP,YAAM,OAAO,KAAK;AAClB,YAAM,OAAO,KAAK;AAClB,eAAS,IAAI,GAAG,KAA6B,IAAI,KAAK,KAAK;AACzD,cAAM,KAAK,CAAC;AACZ,YAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAO,kBAAkB,GAAG,CAAC,IAAI,KAAK,YAAY,KAAK,CAAC,CAAC;AAAA,QAC3D,OAAO;AACL,iBAAO,KAAK,YAAY,GAAG,CAAW,IAAI,KAAK,YAAY,KAAK,CAAC,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,kBACN,MACyB;AACzB,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACJ,KAAK,wBAA+B,CAAC,IAAI,uBAAO,OAAO,IAAI;AAAA,IAI9D;AACA,SAAK,sBAAsB,KAAK,GAAG,MAAM;AACzC,oBAAgB,QAAQ,KAAK,CAAC;AAC9B,WAAO;AAAA,EACT;AAAA,EAEQ,gBAAgB,MAA6B;AACnD,WAAO,KAAK,mBAAmB,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,EACzD;AAAA,EAEQ,kBAAkB,MAAiC;AACzD,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,IAAI,OAAO,kBAAkB,KAAK,CAAC,GAAG,KAAK,CAAC;AAAA,IAC9C;AAAA,EACF;AAAA,EAEQ,eAAe,MAAoC;AACzD,UAAM,SAAS,KAAK,mBAAmB,KAAK,GAAG,oBAAI,IAAa,CAAC;AACjE,UAAM,QAAQ,KAAK;AACnB,aAAS,IAAI,GAAG,MAAM,KAAK,GAAG,IAAI,KAAK,KAAK;AAC1C,aAAO,IAAI,KAAK,YAAY,MAAM,CAAC,CAAC,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,eAAe,MAA6C;AAClE,UAAM,SAAS,KAAK,mBAAmB,KAAK,GAAG,oBAAI,IAAsB,CAAC;AAC1E,UAAM,OAAO,KAAK,EAAE;AACpB,UAAM,OAAO,KAAK,EAAE;AACpB,aAAS,IAAI,GAAG,MAAM,KAAK,EAAE,GAAG,IAAI,KAAK,KAAK;AAC5C,aAAO,IAAI,KAAK,YAAY,KAAK,CAAC,CAAC,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,uBAAuB,MAA2C;AACxE,UAAM,QAAQ,IAAI,WAAW,KAAK,CAAC;AACnC,UAAM,SAAS,KAAK,mBAAmB,KAAK,GAAG,MAAM,MAAM;AAC3D,WAAO;AAAA,EACT;AAAA,EAEQ,sBACN,MACyC;AACzC,UAAM,YAAY,yBAAyB,KAAK,CAAC;AACjD,UAAM,SAAS,KAAK,YAAY,KAAK,CAAC;AACtC,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACL,IAAI,UAAU,QAAQ,KAAK,GAAG,KAAK,CAAC;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,oBAAoB,MAAqC;AAC/D,UAAM,SAAS,KAAK,YAAY,KAAK,CAAC;AACtC,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACL,IAAI,SAAS,QAAQ,KAAK,GAAG,KAAK,CAAC;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,sBACN,MACA,QACG;AACH,QAAI,KAAK,GAAG;AACV,YAAM,SAAS,KAAK,sBAAsB,KAAK,GAAG,CAAC,CAAC;AACpD,aAAO,OAAO,QAAQ,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,0BACN,MACgB;AAEhB,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACL,IAAI,eAAe,CAAC,GAAG,kBAAkB,KAAK,CAAC,CAAC;AAAA,IAClD;AAIA,WAAO,KAAK,sBAAsB,MAAM,MAAM;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+B;AACtD,UAAM,YAAY,kBAAkB,KAAK,CAAC;AAC1C,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACL,IAAI,UAAU,kBAAkB,KAAK,CAAC,CAAC;AAAA,IACzC;AACA,WAAO,KAAK,sBAAsB,MAAM,MAAM;AAAA,EAChD;AAAA,EAEQ,mBAAmB,MAA4C;AACrE,UAAM,WAAW,eAAe;AAChC,UAAM,SAAS,KAAK,mBAAmB,KAAK,GAAG,QAAQ;AACvD,UAAM,eAAe,KAAK,YAAY,KAAK,CAAC;AAC5C,QAAI,KAAK,GAAG;AACV,eAAS,QAAQ,YAAY;AAAA,IAC/B,OAAO;AACL,eAAS,OAAO,YAAY;AAAA,IAC9B;AACA,WAAO,OAAO;AAAA,EAChB;AAAA,EAEQ,iBAAiB,MAAiC;AACxD,WAAO,KAAK,mBAAmB,KAAK,GAAG,OAAO,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC;AAAA,EACzE;AAAA,EAEQ,kBAAkB,MAAkC;AAC1D,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,YAAM,MAAM,kBAAkB,KAAK,CAAC;AACpC,eAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,cAAM,SAAS,eAAe,CAAC;AAC/B,YAAI,OAAO,QAAQ,KAAK;AACtB,iBAAO,KAAK;AAAA,YACV,KAAK;AAAA,YACL,OAAO,YAAY,KAAK,GAAG,MAAM;AAAA,cAC/B,IAAI,KAAK;AAAA,YACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,0BAA0B,KAAK,CAAC;AAAA,EAC5C;AAAA,EAEQ,8BACN,MACS;AACT,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,KAAK,mBAAmB,KAAK,GAAG,eAAe,CAAC,EAAE;AAAA,IACpD;AAAA,EACF;AAAA,EAEQ,0BAA0B,MAA0C;AAC1E,UAAM,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC;AACrC,WAAO,UAAU,IAAI,4BAA4B,SAAS,CAAC;AAC3D,aAAS,QAAQ,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC;AAC5C,WAAO;AAAA,EACT;AAAA,EAEQ,yBAAyB,MAAyC;AACxE,UAAM,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC;AACrC,WAAO,UAAU,IAAI,4BAA4B,SAAS,CAAC;AAC3D,aAAS,OAAO,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3C,WAAO;AAAA,EACT;AAAA,EAEQ,mCACN,MACS;AACT,SAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AAC1B,UAAM,SAAS,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AACzC,WAAO,mBAAmB,MAAkB;AAAA,EAC9C;AAAA,EAEQ,wCACN,MACS;AACT,SAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AAC1B,UAAM,SAAS,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AACzC,WAAO,sBAAsB,MAAqB;AAAA,EACpD;AAAA,EAEQ,6BACN,MACS;AACT,UAAM,SAAS,KAAK,mBAAmB,KAAK,GAAG,aAAa,CAAC;AAC7D,UAAM,MAAM,KAAK,EAAE;AACnB,QAAI,KAAK;AACP,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,sBAAsB,MAAsC;AAClE,UAAM,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC;AACrC,WAAO,UAAU,IAAI,4BAA4B,QAAQ,CAAC;AAC1D,aAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC;AACtC,WAAO;AAAA,EACT;AAAA,EAEQ,uBAAuB,MAAuC;AACpE,UAAM,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC;AACrC,WAAO,UAAU,IAAI,4BAA4B,QAAQ,CAAC;AAC1D,aAAS,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC;AACvC,WAAO;AAAA,EACT;AAAA,EAEQ,wBAAwB,MAAwC;AACtE,UAAM,WAAW,KAAK,KAAK,IAAI,KAAK,CAAC;AACrC,WAAO,UAAU,IAAI,4BAA4B,QAAQ,CAAC;AAC1D,aAAS,OAAO,KAAK,YAAY,KAAK,CAAC,CAAC;AACxC,WAAO;AAAA,EACT;AAAA,EAEQ,2BACN,MACS;AACT,SAAK,YAAY,KAAK,CAAC;AACvB,WAAO;AAAA,EACT;AAAA,EAEQ,gCACN,MACS;AACT,SAAK,YAAY,KAAK,EAAE,CAAC,CAAC;AAC1B,WAAO;AAAA,EACT;AAAA,EAEA,eAAe,MAA4B;AACzC,QAAI;AACF,aAAO,KAAK,YAAY,IAAI;AAAA,IAC9B,SAAS,OAAO;AACd,YAAM,IAAI,4BAA4B,KAAK;AAAA,IAC7C;AAAA,EACF;AAAA,EAEA,YAAY,MAA4B;AACtC,YAAQ,KAAK,GAAG;AAAA,MACd;AACE,eAAO,aAAa,KAAK,CAAC;AAAA,MAC5B;AACE,eAAO,KAAK;AAAA,MACd;AACE,eAAO,kBAAkB,KAAK,CAAC;AAAA,MACjC;AACE,eAAO,OAAO,KAAK,CAAC;AAAA,MACtB;AACE,eAAO,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,MAC7B;AACE,eAAO,KAAK,qBAAqB,IAAI;AAAA,MACvC;AACE,eAAO,KAAK,iBAAiB,IAAI;AAAA,MACnC;AAAA,MACA;AACE,eAAO,KAAK,kBAAkB,IAAI;AAAA,MACpC;AACE,eAAO,KAAK,gBAAgB,IAAI;AAAA,MAClC;AACE,eAAO,KAAK,kBAAkB,IAAI;AAAA,MACpC;AACE,eAAO,KAAK,eAAe,IAAI;AAAA,MACjC;AACE,eAAO,KAAK,eAAe,IAAI;AAAA,MACjC;AACE,eAAO,KAAK,uBAAuB,IAAI;AAAA,MACzC;AAAA,MACA;AACE,eAAO,KAAK,sBAAsB,IAAI;AAAA,MACxC;AACE,eAAO,KAAK,oBAAoB,IAAI;AAAA,MACtC;AACE,eAAO,KAAK,0BAA0B,IAAI;AAAA,MAC5C;AACE,eAAO,KAAK,iBAAiB,IAAI;AAAA,MACnC;AACE,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACrC;AACE,eAAO,WAAW,KAAK,CAAC;AAAA,MAC1B;AACE,eAAO,KAAK,iBAAiB,IAAI;AAAA,MACnC;AACE,eAAO,KAAK,kBAAkB,IAAI;AAAA,MACpC;AACE,eAAO,KAAK,8BAA8B,IAAI;AAAA,MAChD;AACE,eAAO,KAAK,0BAA0B,IAAI;AAAA,MAC5C;AACE,eAAO,KAAK,yBAAyB,IAAI;AAAA,MAC3C;AACE,eAAO,KAAK,mCAAmC,IAAI;AAAA,MACrD;AACE,eAAO,KAAK,wCAAwC,IAAI;AAAA,MAC1D;AACE,eAAO,KAAK,6BAA6B,IAAI;AAAA,MAC/C;AACE,eAAO,KAAK,sBAAsB,IAAI;AAAA,MACxC;AACE,eAAO,KAAK,uBAAuB,IAAI;AAAA,MACzC;AACE,eAAO,KAAK,wBAAwB,IAAI;AAAA,MAC1C;AACE,eAAO,KAAK,2BAA2B,IAAI;AAAA,MAC7C;AACE,eAAO,KAAK,gCAAgC,IAAI;AAAA;AAAA,MAElD;AACE,cAAM,IAAI,4BAA4B,IAAI;AAAA,IAC9C;AAAA,EACF;AACF;;;ACjcA,IAAqB,2BAArB,cAAsD,wBAAwB;AAAA,EAA9E;AAAA;AACE,SAAS,OAAoB;AAAA;AAAA,EAE7B,mBAAsB,OAAe,OAAa;AAChD,QAAI,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG;AACzB,WAAK,KAAK,IAAI,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;;;ACfA,IAAM,mBAAmB;AAElB,SAAS,kBAAkB,MAAuB;AACvD,QAAM,OAAO,KAAK,CAAC;AACnB,UACG,SAAS,OACR,SAAS,OACR,QAAQ,OAAO,QAAQ,OACvB,QAAQ,OAAO,QAAQ,QAC1B,iBAAiB,KAAK,IAAI;AAE9B;;;AC6FA,SAAS,wBAAwB,YAAgC;AAC/D,UAAQ,WAAW,GAAG;AAAA,IACpB,KAAK;AACH,aAAO,WAAW,IAAI,MAAM,WAAW;AAAA,IACzC,KAAK;AACH,aAAO,WAAW,IAAI,UAAU,WAAW,IAAI,MAAM,WAAW,IAAI;AAAA,IACtE,KAAK;AACH,aAAO,WAAW,IAAI,UAAU,WAAW,IAAI;AAAA,IACjD,KAAK;AACH,aAAO,WAAW,IAAI,aAAa,WAAW,IAAI;AAAA,EACtD;AACF;AAEA,SAAS,iBAAiB,aAAyC;AACjE,QAAM,iBAA+B,CAAC;AACtC,MAAI,UAAU,YAAY,CAAC;AAC3B,WACM,IAAI,GAAG,MAAM,YAAY,QAAQ,MAAkB,OAAO,SAC9D,IAAI,KACJ,KACA;AACA,WAAO,YAAY,CAAC;AACpB,QAAI,KAAK,MAAM,iBAAwB,KAAK,MAAM,KAAK,GAAG;AAGxD,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,QACH,GAAG,wBAAwB,OAAO;AAAA,MACpC;AAAA,IACF,WAAW,KAAK,MAAM,eAAsB,KAAK,MAAM,KAAK,GAAG;AAE7D,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,wBAAwB,OAAO;AAAA,QAClC,GAAG,KAAK;AAAA,QACR,GAAG,KAAK;AAAA,MACV;AAAA,IACF,WAAW,KAAK,MAAM,eAAsB,KAAK,MAAM,KAAK,GAAG;AAE7D,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,wBAAwB,OAAO;AAAA,QAClC,GAAG;AAAA,QACH,GAAG,KAAK;AAAA,MACV;AAAA,IACF,WAAW,KAAK,MAAM,kBAAyB,KAAK,MAAM,KAAK,GAAG;AAEhE,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG,wBAAwB,OAAO;AAAA,QAClC,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACL;AAAA,IACF,OAAO;AAEL,qBAAe,KAAK,OAAO;AAC3B,gBAAU;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAEA,iBAAe,KAAK,OAAO;AAE3B,SAAO;AACT;AAEA,SAAS,mBAAmB,aAA+C;AACzE,MAAI,YAAY,QAAQ;AACtB,QAAI,SAAS;AACb,UAAM,SAAS,iBAAiB,WAAW;AAC3C,aAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AACjD,gBAAU,wBAAwB,OAAO,CAAC,CAAC,IAAI;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AAExB,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AAEvB,IAAM,0BACJ;AAAA,EACE,eAA0B,GAAG;AAAA,EAC7B,eAA0B,GAAG;AAAA,EAC7B,sBAAiC,GAAG;AAAA,EACpC,aAAwB,GAAG;AAC7B;AAaF,IAA8B,wBAA9B,MAEA;AAAA,EAgCE,YAAY,SAAuC;AAtBnD;AAAA;AAAA;AAAA;AAAA,iBAAkB,CAAC;AAMnB;AAAA;AAAA;AAAA;AAAA,iBAAyB,CAAC;AAM1B;AAAA;AAAA;AAAA;AAAA,uBAA4B,CAAC;AAW3B,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,QAAQ;AACxB,SAAK,SAAS,IAAI,IAAI,QAAQ,UAAU;AAAA,EAC1C;AAAA,EAIA,eAAe,YAAsB,MAAsB;AACzD,WAAO,eAAe,KAAK,UAAU,YAAY,IAAI;AAAA,EACvD;AAAA,EAEA,wBAAwB,YAAsB,MAAsB;AAClE,WAAO,wBAAwB,KAAK,UAAU,YAAY,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQU,QAAQ,IAAkB;AAClC,SAAK,OAAO,IAAI,EAAE;AAAA,EACpB;AAAA,EAEU,SAAS,IAAqB;AACtC,WAAO,KAAK,OAAO,IAAI,EAAE;AAAA,EAC3B;AAAA,EASU,eAAe,MAA0B,IAAkB;AACnE,QAAI,uBAAkC;AACpC,WAAK,QAAQ,EAAE;AACf,WAAK,MAAM,KAAK;AAAA,QACd,MAAM;AAAA,QACN,OAAO,KAAK,YAAY,EAAE;AAAA,MAC5B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEQ,eAAmC;AACzC,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,UAAU,KAAK,OAAO,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AACxE,YAAM,OAAO,QAAQ,CAAC;AACtB,gBAAU,wBAAwB,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ;AAAA,IACpE;AACA,WAAO;AAAA,EACT;AAAA,EAEU,iBAAqC;AAC7C,UAAM,cAAc,mBAAmB,KAAK,WAAW;AACvD,UAAM,QAAQ,KAAK,aAAa;AAChC,QAAI,aAAa;AACf,UAAI,OAAO;AACT,eAAO,cAAc;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOU,iBAAiB,QAAgB,OAAqB;AAC9D,SAAK,YAAY,KAAK;AAAA,MACpB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEU,oBAAoB,KAAa,OAAqB;AAC9D,SAAK,YAAY,KAAK;AAAA,MACpB,GAAG;AAAA,MACH,GAAG,KAAK,YAAY,GAAG;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEU,oBAAoB,KAAa,KAAa,OAAqB;AAC3E,SAAK,YAAY,KAAK;AAAA,MACpB,GAAG;AAAA,MACH,GAAG,KAAK,YAAY,GAAG;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEU,uBAAuB,KAAa,KAAmB;AAC/D,SAAK,YAAY,KAAK;AAAA,MACpB,GAAG;AAAA,MACH,GAAG,KAAK,YAAY,GAAG;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEU,kBACR,KACA,OACA,OACM;AACN,SAAK,iBAAiB,KAAK,YAAY,GAAG,IAAI,MAAM,QAAQ,KAAK,KAAK;AAAA,EACxE;AAAA,EAEU,mBAAmB,KAAa,KAAa,OAAqB;AAC1E,SAAK,iBAAiB,KAAK,YAAY,GAAG,IAAI,MAAM,KAAK,KAAK;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,MAA4B;AAChD,WACE,KAAK,8BAAsC,KAAK,MAAM,SAAS,KAAK,CAAC;AAAA,EAEzE;AAAA,EAUU,mBAAmB,MAAoC;AAC/D,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,iBAAiB,WAAW,KAAK,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EAEU,mBACR,IACA,MACA,OACQ;AAER,QAAI,MAAM;AAER,UAAI,KAAK,sBAAsB,IAAI,GAAG;AACpC,aAAK,QAAQ,EAAE;AACf,aAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA,KAAK,YAAa,KAAiC,CAAC;AAAA,QACtD;AACA,eAAO;AAAA,MACT;AACA,aAAO,KAAK,UAAU,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EAEU,eAAe,MAAgC;AACvD,UAAM,KAAK,KAAK;AAChB,QAAI,KAAK,GAAG;AACV,WAAK,MAAM,KAAK,EAAE;AAClB,YAAM,OAAO,KAAK;AAClB,UAAI,SAAS,KAAK,mBAAmB,IAAI,KAAK,CAAC,GAAG,CAAC;AAInD,UAAI,UAAU,WAAW;AACzB,eAAS,IAAI,GAAG,MAAM,KAAK,GAAG,MAAc,IAAI,KAAK,KAAK;AACxD,eAAO,KAAK,mBAAmB,IAAI,KAAK,CAAC,GAAG,CAAC;AAC7C,kBAAU,MAAM;AAChB,kBAAU,SAAS;AAAA,MACrB;AACA,WAAK,MAAM,IAAI;AACf,WAAK,eAAe,KAAK,GAAG,KAAK,CAAC;AAClC,aAAO,KAAK,mBAAmB,IAAI,MAAM,UAAU,UAAU,OAAO,IAAI;AAAA,IAC1E;AACA,WAAO,KAAK,mBAAmB,IAAI,IAAI;AAAA,EACzC;AAAA,EAEU,kBACR,QACA,KACA,KACQ;AACR,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,QAAQ,OAAO,GAAG;AACxB,YAAM;AAAA;AAAA;AAAA,QAGH,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQR,MAAM,SAAS,MAAM,OACvB,kBAAkB,GAAG;AAAA;AACvB,UAAI,KAAK,sBAAsB,GAAG,GAAG;AACnC,cAAM,WAAW,KAAK,YAAa,IAAgC,CAAC;AACpE,aAAK,QAAQ,OAAO,CAAC;AAGrB,YAAI,gBAAgB,UAAU,OAAO;AACnC,eAAK,mBAAmB,OAAO,GAAG,KAAK,QAAQ;AAAA,QACjD,OAAO;AACL,eAAK;AAAA,YACH,OAAO;AAAA,YACP,eAAe,MAAM,MAAM,MAAM;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,cAAQ,eAAe,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK,UAAU,GAAG;AAAA,IAC1E;AACA,WAAO,MAAM,KAAK,UAAU,GAAG,IAAI,OAAO,KAAK,UAAU,GAAG;AAAA,EAC9D;AAAA,EAEU,oBACR,QACA,QACQ;AACR,UAAM,MAAM,OAAO;AACnB,QAAI,KAAK;AACP,YAAM,OAAO,OAAO;AACpB,YAAM,SAAS,OAAO;AACtB,WAAK,MAAM,KAAK,OAAO,CAAC;AACxB,UAAI,SAAS,KAAK,kBAAkB,QAAQ,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9D,eAAS,IAAI,GAAG,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC3C,eAAO,KAAK,kBAAkB,QAAQ,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;AACxD,mBAAW,QAAQ,UAAU,OAAO;AAAA,MACtC;AACA,WAAK,MAAM,IAAI;AACf,aAAO,MAAM,SAAS;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EAEU,gBAAgB,MAAiC;AACzD,SAAK,eAAe,KAAK,GAAG,KAAK,CAAC;AAClC,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,KAAK,oBAAoB,MAAM,KAAK,CAAC;AAAA,IACvC;AAAA,EACF;AAAA,EAEU,0BACR,QACA,OACA,YACQ;AACR,UAAM,SAAS,KAAK,oBAAoB,QAAQ,KAAK;AACrD,QAAI,WAAW,MAAM;AACnB,aAAO,mBAAmB,aAAa,MAAM,SAAS;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,6BACN,QACA,iBACA,KACA,OACM;AACN,UAAM,aAAa,KAAK,UAAU,KAAK;AACvC,UAAM,QAAQ,OAAO,GAAG;AACxB,UAAM;AAAA;AAAA;AAAA,MAGH,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQR,MAAM,SAAS,MAAM,OACvB,kBAAkB,GAAG;AAAA;AACvB,QAAI,KAAK,sBAAsB,KAAK,GAAG;AAGrC,UAAI,gBAAgB,UAAU,OAAO;AACnC,aAAK,mBAAmB,OAAO,GAAG,KAAK,UAAU;AAAA,MACnD,OAAO;AACL,aAAK;AAAA,UACH,OAAO;AAAA,UACP,eAAe,MAAM,MAAM,MAAM;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,mBAAmB,KAAK;AAC9B,WAAK,cAAc;AACnB,UAAI,gBAAgB,UAAU,OAAO;AACnC,aAAK,mBAAmB,OAAO,GAAG,KAAK,UAAU;AAAA,MACnD,OAAO;AACL,aAAK;AAAA,UACH,OAAO;AAAA,UACP,eAAe,MAAM,MAAM,MAAM;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEU,oBACR,QACA,iBACA,KACA,OACM;AACN,QAAI,OAAO,QAAQ,UAAU;AAC3B,WAAK,6BAA6B,QAAQ,iBAAiB,KAAK,KAAK;AAAA,IACvE,OAAO;AACL,YAAM,SAAS,KAAK;AACpB,WAAK,QAAQ,CAAC;AACd,YAAM,aAAa,KAAK,UAAU,KAAK;AACvC,WAAK,QAAQ;AACb,YAAM,mBAAmB,KAAK;AAC9B,WAAK,cAAc;AACnB,WAAK,kBAAkB,OAAO,GAAG,KAAK,UAAU,GAAG,GAAG,UAAU;AAChE,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEU,qBACR,QACA,MACoB;AACpB,UAAM,MAAM,KAAK;AACjB,QAAI,KAAK;AACP,YAAM,kBAAgC,CAAC;AACvC,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK;AACpB,WAAK,MAAM,KAAK,OAAO,CAAC;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,aAAK,oBAAoB,QAAQ,iBAAiB,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACtE;AACA,WAAK,MAAM,IAAI;AACf,aAAO,mBAAmB,eAAe;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EAEU,oBACR,MACA,MACQ;AACR,QAAI,KAAK,GAAG;AACV,UAAI,KAAK,iCAAiC;AACxC,eAAO,KAAK,0BAA0B,MAAM,KAAK,GAAG,IAAI;AAAA,MAC1D,OAAO;AACL,aAAK,QAAQ,KAAK,CAAC;AACnB,cAAM,cAAc,KAAK,qBAAqB,MAAM,KAAK,CAAC;AAC1D,YAAI,aAAa;AACf,iBACE,MACA,KAAK,mBAAmB,KAAK,GAAG,IAAI,IACpC,MACA,cACA,KAAK,YAAY,KAAK,CAAC,IACvB;AAAA,QAEJ;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,mBAAmB,KAAK,GAAG,IAAI;AAAA,EAC7C;AAAA,EAEU,yBAAyB,MAA0C;AAC3E,SAAK,eAAe,KAAK,GAAG,KAAK,CAAC;AAClC,WAAO,KAAK,oBAAoB,MAAM,gBAAgB;AAAA,EACxD;AAAA,EAEU,cAAc,MAA+B;AACrD,WAAO,KAAK,mBAAmB,KAAK,GAAG,eAAe,KAAK,IAAI,IAAI;AAAA,EACrE;AAAA,EAEU,gBAAgB,MAAiC;AACzD,WAAO,KAAK,mBAAmB,KAAK,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AAAA,EACpE;AAAA,EAEU,iBAAiB,IAAY,MAA2B;AAChE,QAAI,KAAK,sBAAsB,IAAI,GAAG;AACpC,WAAK,QAAQ,EAAE;AACf,WAAK;AAAA,QACH;AAAA,QACA,KAAK,YAAa,KAAiC,CAAC;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AACA,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AAAA,EAEU,aAAa,MAA8B;AACnD,QAAI,aAAa;AACjB,UAAM,OAAO,KAAK;AAClB,UAAM,KAAK,KAAK;AAChB,QAAI,MAAM;AACR,YAAM,QAAQ,KAAK;AACnB,WAAK,MAAM,KAAK,EAAE;AAClB,UAAI,SAAS,KAAK,iBAAiB,IAAI,MAAM,CAAC,CAAC;AAC/C,eAAS,IAAI,GAAG,OAAO,QAAQ,IAAI,MAAM,KAAK;AAC5C,eAAO,KAAK,iBAAiB,IAAI,MAAM,CAAC,CAAC;AACzC,mBAAW,QAAQ,UAAU,OAAO;AAAA,MACtC;AACA,WAAK,MAAM,IAAI;AACf,UAAI,QAAQ;AACV,sBAAc,OAAO,SAAS;AAAA,MAChC;AAAA,IACF;AACA,WAAO,KAAK,mBAAmB,IAAI,UAAU;AAAA,EAC/C;AAAA,EAEU,kBACR,IACA,KACA,KACA,UACQ;AACR,QAAI,KAAK,sBAAsB,GAAG,GAAG;AAEnC,YAAM,SAAS,KAAK,YAAa,IAAgC,CAAC;AAClE,WAAK,QAAQ,EAAE;AAEf,UAAI,KAAK,sBAAsB,GAAG,GAAG;AACnC,cAAM,WAAW,KAAK,YAAa,IAAgC,CAAC;AAIpE,aAAK,oBAAoB,IAAI,QAAQ,QAAQ;AAC7C,eAAO;AAAA,MACT;AAMA,UACE,IAAI,8BACJ,IAAI,KAAK,QACT,KAAK,SAAS,IAAI,CAAC,GACnB;AAKA,cAAM,aACJ,MAAM,KAAK,UAAU,GAAG,IAAI,OAAO,WAAW,MAAM,WAAW;AACjE,aAAK,oBAAoB,IAAI,QAAQ,KAAK,YAAY,IAAI,CAAC,CAAC;AAC5D,aAAK,uBAAuB,IAAI,QAAQ;AACxC,eAAO;AAAA,MACT;AACA,YAAM,SAAS,KAAK;AACpB,WAAK,QAAQ,CAAC;AACd,WAAK,oBAAoB,IAAI,QAAQ,KAAK,UAAU,GAAG,CAAC;AACxD,WAAK,QAAQ;AACb,aAAO;AAAA,IACT;AACA,QAAI,KAAK,sBAAsB,GAAG,GAAG;AAEnC,YAAM,WAAW,KAAK,YAAa,IAAgC,CAAC;AACpE,WAAK,QAAQ,EAAE;AACf,UACE,IAAI,8BACJ,IAAI,KAAK,QACT,KAAK,SAAS,IAAI,CAAC,GACnB;AACA,cAAM,aACJ,MAAM,KAAK,UAAU,GAAG,IAAI,OAAO,WAAW,MAAM,WAAW;AACjE,aAAK,oBAAoB,IAAI,KAAK,YAAY,IAAI,CAAC,GAAG,QAAQ;AAC9D,aAAK,uBAAuB,IAAI,QAAQ;AACxC,eAAO;AAAA,MACT;AAEA,YAAM,SAAS,KAAK;AACpB,WAAK,QAAQ,CAAC;AACd,WAAK,oBAAoB,IAAI,KAAK,UAAU,GAAG,GAAG,QAAQ;AAC1D,WAAK,QAAQ;AACb,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,KAAK,UAAU,GAAG,IAAI,MAAM,KAAK,UAAU,GAAG,IAAI;AAAA,EACjE;AAAA,EAEU,aAAa,MAA8B;AACnD,QAAI,aAAa;AACjB,UAAM,OAAO,KAAK,EAAE;AACpB,UAAM,KAAK,KAAK;AAChB,UAAM,WAAW,KAAK;AACtB,UAAM,aAAa,KAAK,YAAY,SAAS,CAAC;AAC9C,QAAI,MAAM;AACR,YAAM,OAAO,KAAK,EAAE;AACpB,YAAM,OAAO,KAAK,EAAE;AACpB,WAAK,MAAM,KAAK,EAAE;AAClB,UAAI,SAAS,KAAK,kBAAkB,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,UAAU;AACpE,eAAS,IAAI,GAAG,OAAO,QAAQ,IAAI,MAAM,KAAK;AAC5C,eAAO,KAAK,kBAAkB,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,UAAU;AAC9D,mBAAW,QAAQ,UAAU,OAAO;AAAA,MACtC;AACA,WAAK,MAAM,IAAI;AAIf,UAAI,QAAQ;AACV,sBAAc,OAAO,SAAS;AAAA,MAChC;AAAA,IACF;AACA,QAAI,SAAS,iCAAwC;AACnD,WAAK,QAAQ,SAAS,CAAC;AACvB,mBAAa,MAAM,KAAK,UAAU,QAAQ,IAAI,MAAM,aAAa;AAAA,IACnE;AACA,WAAO,KAAK,mBAAmB,IAAI,UAAU;AAAA,EAC/C;AAAA,EAEU,qBAAqB,MAAsC;AACnE,QAAI,SAAS;AACb,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,OAAO;AACnB,QAAI,KAAK;AACP,gBAAU,MAAM,OAAO,CAAC;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAU,MAAM,OAAO,CAAC;AAAA,MAC1B;AACA,gBAAU;AAAA,IACZ;AACA,WAAO,KAAK,mBAAmB,KAAK,GAAG,SAAS,UAAU;AAAA,EAC5D;AAAA,EAEU,oBACR,MACQ;AACR,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,SACE,KAAK,IACL,MACA,KAAK,UAAU,KAAK,CAAC,IACrB,MACA,KAAK,IACL,MACA,KAAK,IACL;AAAA,IACJ;AAAA,EACF;AAAA,EAEU,kBAAkB,MAAmC;AAC7D,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,kBACE,KAAK,UAAU,KAAK,CAAC,IACrB,MACA,KAAK,IACL,MACA,KAAK,IACL;AAAA,IACJ;AAAA,EACF;AAAA,EAEU,wBAAwB,MAAyC;AACzE,UAAM,KAAK,KAAK;AAIhB,SAAK,MAAM,KAAK,EAAE;AAClB,UAAM,aAAa,KAAK;AAAA,MACtB;AAAA,MACA,4BAA4B,KAAK,IAAI;AAAA,IACvC;AACA,SAAK,MAAM,IAAI;AACf,WAAO;AAAA,EACT;AAAA,EAEU,eAAe,MAAgC;AACvD,WAAO,KAAK;AAAA,MACV;AAAA,MACA,SAAS,yBAAyB,KAAK,CAAC,IAAI,OAAO,KAAK,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA,EAEU,iBAAiB,MAAkC;AAC3D,QAAI;AAEJ,UAAM,YAAY,KAAK;AACvB,UAAM,KAAK,KAAK;AAChB,UAAM,qBAAqB,KAAK,IAAI,kBAAkB;AACtD,QAAI,KAAK,sBAAsB,SAAS,GAAG;AAKzC,YAAM,MAAM,KAAK,YAAa,UAAsC,CAAC;AACrE,mBACE,sBACC,KAAK,IACF,aAAa,KAAK,eAAe,CAAC,GAAG,GAAG,IAAI,MAC5C,cACA,KAAK,wBAAwB,CAAC,GAAG,WAAW,GAAG,IAC/C;AAAA,IACR,OAAO;AACL,WAAK,MAAM,KAAK,EAAE;AAClB,YAAM,SAAS,KAAK,UAAU,SAAS;AACvC,WAAK,MAAM,IAAI;AAEf,mBAAa,qBAAqB,MAAM,SAAS;AAAA,IACnD;AACA,WAAO,KAAK,mBAAmB,IAAI,UAAU;AAAA,EAC/C;AAAA,EAEU,yBAAyB,MAAmC;AACpE,WAAO,KAAK,mBAAmB,KAAK,GAAG,cAAc,KAAK,CAAC,CAAC;AAAA,EAC9D;AAAA,EAEU,eAAe,MAAgC;AACvD,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,YAAY,KAAK,UAAU,KAAK,CAAC,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EAEU,gBAAgB,MAAiC;AACzD,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,eAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,cAAM,SAAS,eAAe,CAAC;AAC/B,YAAI,OAAO,QAAQ,KAAK,GAAG;AACzB,iBAAO,KAAK;AAAA,YACV,KAAK;AAAA,YACL,OAAO,UAAU,KAAK,GAAG,MAAM;AAAA,cAC7B,IAAI,KAAK;AAAA,YACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,IAAI,0BAA0B,KAAK,CAAC;AAAA,EAC5C;AAAA,EAEQ,eAAe,MAAiC;AACtD,UAAM,UAAU,KAAK,UAAU,IAAI;AACnC,WAAO,YAAY,KAAK,YAAY,KAAK,CAAC,IAAI,UAAU,MAAM,UAAU;AAAA,EAC1E;AAAA,EAEU,4BACR,MACQ;AACR,UAAM,WAAW,KAAK,mBAAmB,KAAK,GAAG,eAAe;AAChE,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,KAAK,eAAe,KAAK,CAAC,IAAI,MAAM,WAAW;AAAA,IACjD;AAAA,EACF;AAAA,EAEU,wBAAwB,MAAyC;AACzE,WACE,KAAK,eAAe,KAAK,EAAE,CAAC,CAAC,IAC7B,MACA,KAAK,YAAY,KAAK,CAAC,IACvB,MACA,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC,IACxB;AAAA,EAEJ;AAAA,EAEU,uBAAuB,MAAwC;AACvE,WACE,KAAK,eAAe,KAAK,EAAE,CAAC,CAAC,IAC7B,MACA,KAAK,YAAY,KAAK,CAAC,IACvB,MACA,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC,IACxB;AAAA,EAEJ;AAAA,EAEU,0BACR,MACQ;AACR,WAAO,KAAK;AAAA,MACV,KAAK;AAAA,MACL,+BAA+B,KAAK,UAAU,KAAK,CAAC;AAAA,IACtD;AAAA,EACF;AAAA,EAEU,yBAAyB,MAA0C;AAC3E,QAAI,SAAS;AACb,QAAI,cAAc;AAClB,QAAI,KAAK,EAAE,4BAAoC;AAC7C,WAAK,QAAQ,KAAK,EAAE,CAAC;AACrB,eAAS,MAAM,KAAK,UAAU,KAAK,CAAC,IAAI;AACxC,oBAAc;AAAA,IAChB;AACA,cAAU,KAAK;AAAA,MACb,KAAK;AAAA,MACL,KAAK;AAAA,QACH,CAAC,GAAG;AAAA,QACJ,KAAK;AAAA,UACH,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,UACnB,cACE,KAAK,YAAY,KAAK,EAAE,CAAC,IACzB,OACA,KAAK,eAAe,CAAC,GAAG,GAAG,IAC3B,WACA,KAAK;AAAA,YACH,CAAC;AAAA,YACD;AAAA,UACF,IACA;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,QAAI,aAAa;AACf,gBAAU;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA,EAEU,iCACR,MACQ;AACR,WACE,KAAK,eAAe,KAAK,EAAE,CAAC,CAAC,IAAI,MAAM,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AAAA,EAEvE;AAAA,EAEU,8BACR,MACQ;AACR,UAAM,UAAU,KAAK,EAAE,CAAC;AACxB,UAAM,SAAS,KAAK,EAAE,CAAC;AAEvB,QAAI,SAAS;AAEb,QAAI,QAAQ,4BAAoC;AAC9C,WAAK,QAAQ,QAAQ,CAAC;AACtB,gBAAU,MAAM,KAAK,UAAU,OAAO;AAAA,IACxC;AACA,QAAI,OAAO,4BAAoC;AAC7C,WAAK,QAAQ,OAAO,CAAC;AACrB,iBAAW,SAAS,MAAM,OAAO,KAAK,UAAU,MAAM;AAAA,IACxD;AACA,QAAI,QAAQ;AACV,gBAAU;AAAA,IACZ;AAEA,UAAM,WAAW,KAAK;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,QACH,CAAC,GAAG;AAAA,QACJ,KAAK;AAAA,UACH,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUlC,gCACE,KAAK;AAAA,YACH,CAAC,KAAK,GAAG;AAAA,YACT;AAAA,UACF,IACA,iBACA,KAAK;AAAA,YACH,CAAC,KAAK,GAAG;AAAA,YACT;AAAA,UACF,IACA,YACA,KAAK;AAAA,YACH,CAAC,KAAK,GAAG;AAAA,YACT;AAAA,UACF,IACA,aACA,KAAK;AAAA,YACH,CAAC,KAAK,GAAG;AAAA,YACT;AAAA,UACF,IACA,YACA,KAAK,YAAY,OAAO,CAAC,IACzB,OACA,KAAK,eAAe,CAAC,GAAG,KAAK,IAC7B,WACA,KAAK;AAAA,YACH,CAAC,KAAK,KAAK,GAAG;AAAA,YACd,2CACE,KAAK,YAAY,QAAQ,CAAC,IAC1B;AAAA,UACJ,IACA;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ;AACV,aAAO,SAAS,WAAW;AAAA,IAC7B;AAEA,WAAO;AAAA,EACT;AAAA,EAEU,sCACR,MACQ;AACR,WACE,KAAK,eAAe,KAAK,EAAE,CAAC,CAAC,IAAI,MAAM,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC,IAAI;AAAA,EAEvE;AAAA,EAEU,2BACR,MACQ;AACR,UAAM,SAAS,KAAK;AAAA,MAClB,KAAK;AAAA,MACL,KAAK,eAAe,KAAK,CAAC,IAAI;AAAA,IAChC;AACA,UAAM,MAAM,KAAK,EAAE;AACnB,QAAI,KAAK;AACP,UAAI,SAAS,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC;AACrC,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAU,MAAM,KAAK,UAAU,KAAK,EAAE,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO,MAAM,SAAS,MAAM,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,IACxE;AACA,WAAO;AAAA,EACT;AAAA,EAEU,oBAAoB,MAAqC;AACjE,WAAO,KAAK,YAAY,KAAK,CAAC,IAAI,WAAW,KAAK,UAAU,KAAK,CAAC,IAAI;AAAA,EACxE;AAAA,EAEU,qBAAqB,MAAsC;AACnE,WAAO,KAAK,YAAY,KAAK,CAAC,IAAI,YAAY,KAAK,UAAU,KAAK,CAAC,IAAI;AAAA,EACzE;AAAA,EAEU,sBAAsB,MAAuC;AACrE,WAAO,KAAK,YAAY,KAAK,CAAC,IAAI,aAAa,KAAK,UAAU,KAAK,CAAC,IAAI;AAAA,EAC1E;AAAA,EAEA,UAAU,MAA2B;AACnC,QAAI;AACF,cAAQ,KAAK,GAAG;AAAA,QACd;AACE,iBAAO,gBAAgB,KAAK,CAAC;AAAA,QAC/B;AACE,iBAAO,KAAK,KAAK;AAAA,QACnB;AACE,iBAAO,MAAM,KAAK,IAAI;AAAA,QACxB;AACE,iBAAO,KAAK,IAAI;AAAA,QAClB;AACE,iBAAO,KAAK,YAAY,KAAK,CAAC;AAAA,QAChC;AACE,iBAAO,KAAK,mBAAmB,IAAI;AAAA,QACrC;AACE,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC;AACE,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC;AACE,iBAAO,KAAK,yBAAyB,IAAI;AAAA,QAC3C;AACE,iBAAO,KAAK,cAAc,IAAI;AAAA,QAChC;AACE,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC;AACE,iBAAO,KAAK,aAAa,IAAI;AAAA,QAC/B;AACE,iBAAO,KAAK,aAAa,IAAI;AAAA,QAC/B;AACE,iBAAO,KAAK,qBAAqB,IAAI;AAAA,QACvC;AAAA,QACA;AACE,iBAAO,KAAK,oBAAoB,IAAI;AAAA,QACtC;AACE,iBAAO,KAAK,kBAAkB,IAAI;AAAA,QACpC;AACE,iBAAO,KAAK,wBAAwB,IAAI;AAAA,QAC1C;AACE,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC;AACE,iBAAO,KAAK,iBAAiB,IAAI;AAAA,QACnC;AACE,iBAAO,KAAK,yBAAyB,IAAI;AAAA,QAC3C;AACE,iBAAO,KAAK,eAAe,IAAI;AAAA,QACjC;AACE,iBAAO,KAAK,4BAA4B,IAAI;AAAA,QAC9C;AACE,iBAAO,KAAK,wBAAwB,IAAI;AAAA,QAC1C;AACE,iBAAO,KAAK,uBAAuB,IAAI;AAAA,QACzC;AACE,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC;AACE,iBAAO,KAAK,0BAA0B,IAAI;AAAA,QAC5C;AACE,iBAAO,KAAK,yBAAyB,IAAI;AAAA,QAC3C;AACE,iBAAO,KAAK,iCAAiC,IAAI;AAAA,QACnD;AACE,iBAAO,KAAK,8BAA8B,IAAI;AAAA,QAChD;AACE,iBAAO,KAAK,sCAAsC,IAAI;AAAA,QACxD;AACE,iBAAO,KAAK,2BAA2B,IAAI;AAAA,QAC7C;AACE,iBAAO,KAAK,oBAAoB,IAAI;AAAA,QACtC;AACE,iBAAO,KAAK,qBAAqB,IAAI;AAAA,QACvC;AACE,iBAAO,KAAK,sBAAsB,IAAI;AAAA,QACxC;AACE,gBAAM,IAAI,4BAA4B,IAAI;AAAA,MAC9C;AAAA,IACF,SAAS,OAAO;AACd,YAAM,IAAI,0BAA0B,KAAK;AAAA,IAC3C;AAAA,EACF;AACF;;;AC9oCA,IAAqB,yBAArB,cAAoD,sBAAsB;AAAA,EAKxE,YAAY,SAAwC;AAClD,UAAM,OAAO;AALf,SAAS,OAAoB;AAM3B,SAAK,UAAU,QAAQ;AAAA,EACzB;AAAA,EAEA,YAAY,IAAoB;AAC9B,WAAO,4BAA4B,MAAM,KAAK;AAAA,EAChD;AAAA,EAEU,mBAAmB,OAAe,OAAuB;AAKjE,WAAO,KAAK,YAAY,KAAK,IAAI,MAAM;AAAA,EACzC;AAAA,EAEA,aAAa,MAA2B;AAEtC,UAAM,SAAS,KAAK,UAAU,IAAI;AAGlC,UAAM,KAAK,KAAK;AAChB,QAAI,MAAM,MAAM;AACd,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,KAAK,eAAe;AAEpC,UAAM,MAAM,KAAK,YAAY,EAAE;AAE/B,UAAM,SAAS,KAAK,WAAW,OAAO,KAAK;AAE3C,UAAM,OAAO,UAAU,MAAM,SAAS,MAAM,UAAU,MAAM,MAAM;AAElE,QAAI,WAAW,IAAI;AACjB,UAAI,KAAK,yBAAgC,CAAC,SAAS;AACjD,eAAO,MAAM,OAAO;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAEA,UAAM,OACJ,KAAK,WAAW,OACZ,OACA,MACA,4BACA,OACA,gBAAgB,KAAK,OAAO,IAC5B;AAEN,WAAO,MAAM,KAAK,eAAe,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM;AAAA,EAC3D;AACF;;;ACNA,IAA8B,wBAA9B,cAA4D,kBAAkB;AAAA,EAClE,WAAW,SAAmC;AACtD,UAAM,QAAQ,CAAC;AACf,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,UAAI,KAAK,SAAS;AAChB,cAAM,CAAC,IAAI,KAAK,MAAM,QAAQ,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEU,WAAW,IAAY,SAAsC;AACrE,WAAO,gBAAgB,IAAI,SAAS,KAAK,WAAW,OAAO,CAAC;AAAA,EAC9D;AAAA,EAEU,gBACR,YACyB;AACzB,UAAM,UAAU,OAAO,QAAQ,UAAU;AACzC,UAAM,WAAqC,CAAC;AAC5C,UAAM,aAA4B,CAAC;AACnC,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,eAAS,KAAK,gBAAgB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,iBAAW,KAAK,KAAK,MAAM,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAC3C;AAEA,QAAI,SAAS,OAAO;AACpB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,qBAAqB;AAAA,UAC1B,KAAK;AAAA,YACH,mBAAmB,UAA0C;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,0BAA0B;AAAA,UAC/B,KAAK,MAAM,aAAa,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,iBAAiB,WAAW,MAAM,CAAW,CAAC;AAAA,IAChE;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,WAAW,MAAM,IAAI,YAAY,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA,EAEU,iBACR,IACA,SACA,OACgB;AAChB,WAAO,KAAK;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,gBAAgB,OAAO;AAAA,IAC9B;AAAA,EACF;AAAA,EAEU,WAAW,IAAY,SAAmC;AAClE,WAAO,gBAAgB,IAAI,KAAK,MAAM,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAC1D;AAAA,EAEU,gBACR,IACA,SACuB;AACvB,WAAO,qBAAqB,IAAI,SAAS,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,EACrE;AAAA,EAEU,sBACR,IACA,SAC6B;AAC7B,WAAO,2BAA2B,IAAI,SAAS,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,EAC3E;AAAA,EAEU,cAAc,IAAY,SAAwC;AAC1E,WAAO,mBAAmB,IAAI,SAAS,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,EACnE;AAAA,EAEU,WAAW,IAAY,SAAkC;AACjE,UAAM,UAAU,gBAAgB,SAAS,KAAK,QAAQ;AACtD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU,KAAK,gBAAgB,OAAO,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EAEU,oBACR,IACA,SAC2B;AAC3B,UAAM,UAAU,gBAAgB,SAAS,KAAK,QAAQ;AACtD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,UAAU,KAAK,gBAAgB,OAAO,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EAEU,SACR,IACA,SACgB;AAChB,UAAM,WAA0B,CAAC;AACjC,UAAM,aAA4B,CAAC;AACnC,eAAW,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAC5C,eAAS,KAAK,KAAK,MAAM,GAAG,CAAC;AAC7B,iBAAW,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,IACnC;AACA,WAAO,KAAK,cAAc,IAAI,UAAU,YAAY,QAAQ,IAAI;AAAA,EAClE;AAAA,EAEU,SAAS,IAAY,SAAuC;AACpE,UAAM,QAAuB,CAAC;AAC9B,eAAW,QAAQ,QAAQ,KAAK,GAAG;AACjC,YAAM,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,IAC7B;AACA,WAAO,cAAc,IAAI,QAAQ,MAAM,KAAK;AAAA,EAC9C;AAAA,EAEU,YACR,IACA,SAC+B;AAC/B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,eAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,cAAM,SAAS,eAAe,CAAC;AAC/B,YAAI,OAAO,MAAM,QAAQ,OAAO,KAAK,OAAO,GAAG;AAC7C,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,YACP,OAAO,MAAM,KAAK,SAAS,MAAM;AAAA,cAC/B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEU,YAAY,IAAY,UAAwC;AACxE,WAAO;AAAA,MACL;AAAA,MACA,KAAK,+CAAwD;AAAA,MAC7D,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEU,aACR,IACA,UAC+B;AAC/B,WAAO,KAAK,6BAA6B,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC;AAAA,EACnE;AAAA;AAAA,EAGU,YAAY,IAAY,SAA8B;AAC9D,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,aAAO,KAAK,WAAW,IAAI,OAAO;AAAA,IACpC;AACA,QAAI,SAAS,OAAO,GAAG;AACrB,aAAO,KAAK,YAAY,IAAI,OAAO;AAAA,IACrC;AACA,UAAM,eAAe,QAAQ;AAC7B,QAAI,iBAAiB,iBAAiB;AACpC,aAAO,KAAK;AAAA,QACT,QAA8C;AAAA,MACjD;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,IAAI,OAAO;AAC3C,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,eAAO,KAAK;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,KAAK;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,KAAK;AACH,eAAO,eAAe,IAAI,OAA0B;AAAA,MACtD,KAAK;AACH,eAAO,iBAAiB,IAAI,OAA4B;AAAA,MAC1D,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,WAAW,IAAI,OAA2B;AAAA,MACxD,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,WAAW,IAAI,OAAO;AAAA,MACpC,KAAK;AACH,eAAO,sBAAsB,IAAI,OAAiC;AAAA,MACpE,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,KAAK,gBAAgB,IAAI,OAAqC;AAAA,MACvE,KAAK;AACH,eAAO,KAAK,cAAc,IAAI,OAA8B;AAAA,MAC9D,KAAK;AACH,eAAO,KAAK,SAAS,IAAI,OAA2C;AAAA,MACtE,KAAK;AACH,eAAO,KAAK,SAAS,IAAI,OAAkC;AAAA,MAC7D;AACE;AAAA,IACJ;AAEA,QAAI,iBAAiB,WAAW,mBAAmB,SAAS;AAC1D,aAAO,KAAK,aAAa,IAAI,OAAsC;AAAA,IACrE;AACA,UAAM,kBAAkB,KAAK;AAE7B,QAAI,6CAA4C;AAC9C,cAAQ,cAAc;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,KAAK;AAAA,YACV;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF;AACA,QACE,4CACA,OAAO,mBAAmB,gBACzB,iBAAiB,kBAAkB,mBAAmB,iBACvD;AACA,aAAO,KAAK,oBAAoB,IAAI,OAAoC;AAAA,IAC1E;AAGA,QAAI,mBAAmB,OAAO;AAC5B,aAAO,KAAK,WAAW,IAAI,OAAO;AAAA,IACpC;AAGA,QAAI,OAAO,YAAY,WAAW,OAAO,iBAAiB,SAAS;AACjE,aAAO,KAAK,iBAAiB,IAAI,SAAS,CAAC,CAAC,YAAY;AAAA,IAC1D;AACA,UAAM,IAAI,4BAA4B,OAAO;AAAA,EAC/C;AAAA,EAEU,cAAc,SAA+B;AACrD,UAAM,MAAM,KAAK,aAAa,OAAO;AACrC,QAAI,IAAI,wBAA+B;AACrC,aAAO,IAAI;AAAA,IACb;AACA,UAAM,SAAS,KAAK,YAAY,IAAI,OAAO,OAAO;AAClD,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,IAAI,4BAA4B,OAAO;AAAA,EAC/C;AAAA,EAEA,MAAS,SAAyB;AAChC,YAAQ,OAAO,SAAS;AAAA,MACtB,KAAK;AACH,eAAO,UAAU,YAAY;AAAA,MAC/B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK;AACH,eAAO,iBAAiB,OAAiB;AAAA,MAC3C,KAAK,UAAU;AACb,YAAI,SAAS;AACX,gBAAM,MAAM,KAAK,aAAa,OAAO;AACrC,iBAAO,IAAI,yBACP,KAAK,YAAY,IAAI,OAAO,OAAiB,IAC7C,IAAI;AAAA,QACV;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AACH,eAAO,KAAK,qBAAqB,OAAO;AAAA,MAC1C,KAAK,YAAY;AACf,eAAO,KAAK,cAAc,OAAO;AAAA,MACnC;AAAA,MACA;AACE,cAAM,IAAI,4BAA4B,OAAO;AAAA,IACjD;AAAA,EACF;AAAA,EAEA,SAAY,SAAyB;AACnC,QAAI;AACF,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B,SAAS,OAAO;AACd,YAAM,iBAAiB,qBACnB,QACA,IAAI,mBAAmB,KAAK;AAAA,IAClC;AAAA,EACF;AACF;;;AChXA,IAA8B,0BAA9B,cAA8D,sBAAsB;AAAA,EAalF,YAAY,SAAyC;AACnD,UAAM,OAAO;AAZf;AAAA,SAAQ,QAAQ;AAGhB;AAAA,SAAQ,UAAU;AAelB,SAAQ,UAAU;AAElB,SAAQ,SAAwB,CAAC;AAP/B,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EAMQ,gBAAgB,MAAmB,SAAwB;AACjE,QAAI;AACF,WAAK,gBAAgB,MAAM,OAAO;AAAA,IACpC,SAAS,OAAO;AACd,WAAK,QAAQ,KAAK;AAAA,IACpB;AAAA,EACF;AAAA,EAEQ,QAAc;AACpB,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAI,KAAK,KAAK;AACtD,WAAK,gBAAgB,KAAK,OAAO,CAAC,GAAG,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EAEA,QAAQ,MAAyB;AAC/B,QAAI,KAAK,SAAS;AAChB,WAAK,OAAO,KAAK,IAAI;AAAA,IACvB,OAAO;AACL,WAAK,gBAAgB,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,QAAQ,OAAsB;AAC5B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,KAAK;AAAA,IAC5B,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEQ,SAAe;AACrB,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,mBAAyB;AACvB,SAAK;AAAA,EACP;AAAA,EAEA,kBAAwB;AACtB,QAAI,EAAE,KAAK,WAAW,GAAG;AACvB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EAEU,gBACR,YACyB;AACzB,UAAM,UAAU,OAAO,QAAQ,UAAU;AACzC,UAAM,WAAqC,CAAC;AAC5C,UAAM,aAA4B,CAAC;AACnC,aAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,eAAS,KAAK,gBAAgB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,iBAAW,KAAK,KAAK,MAAM,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAC3C;AAEA,QAAI,SAAS,OAAO;AACpB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,qBAAqB;AAAA,UAC1B,KAAK;AAAA,YACH,mBAAmB,UAA0C;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW;AAAA,QACT;AAAA,UACE,KAAK,0BAA0B;AAAA,UAC/B,KAAK;AAAA,YACH;AAAA,cACE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,iBAAiB,WAAW,MAAM,CAAW,CAAC;AAAA,IAChE;AACA,aAAS,OAAO;AAChB,QAAI,UAAU,YAAY;AACxB,eAAS,KAAK,KAAK,qBAAqB,MAAM,CAAC;AAC/C,iBAAW,KAAK,WAAW,MAAM,IAAI,YAAY,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA,EAEU,qBAAqB,IAAY,MAAqB;AAC9D,UAAM,SAAS,KAAK,eAAe,IAAI;AACvC,QAAI,QAAQ;AACV,WAAK;AAAA,QACH;AAAA;AAAA,UAEE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,CAAC,KAAK,4CAAqD,GAAG,MAAM;AAAA,UACpE;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEU,qBAAqB,IAAY,MAAqB;AAC9D,QAAI,KAAK,OAAO;AACd,YAAM,SAAS,KAAK,eAAe,IAAI;AACvC,UAAI,QAAQ;AACV,aAAK;AAAA,UACH;AAAA;AAAA,YAEE;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,cACE,KAAK,4CAAqD;AAAA,cAC1D;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EAEU,aACR,IACA,SAC+B;AAC/B,UAAM,WAAW,KAAK,YAAY,CAAC,CAAC;AACpC,YAAQ;AAAA,MACN,KAAK,qBAAqB,KAAK,MAAM,QAAQ;AAAA,MAC7C,KAAK,qBAAqB,KAAK,MAAM,QAAQ;AAAA,IAC/C;AACA,SAAK,iBAAiB;AACtB,WAAO,KAAK,6BAA6B,IAAI,QAAQ;AAAA,EACvD;AAAA,EAEU,YACR,IACA,SAC+B;AAC/B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,eAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,cAAM,SAAS,eAAe,CAAC;AAC/B,YAAI,OAAO,MAAM,UAAU,OAAO,KAAK,OAAO,GAAG;AAC/C,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,YACP,OAAO,MAAM,OAAO,SAAS,MAAM;AAAA,cACjC;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEU,YAAY,IAAY,SAAuC;AACvE,UAAM,SAAS;AAAA,MACb;AAAA,MACA,KAAK,+CAAwD;AAAA,MAC7D,CAAC;AAAA,IACH;AACA,SAAK,iBAAiB;AACtB,YAAQ,GAAG;AAAA,MACT,MAAM,WAAS;AACb,YAAI,KAAK,OAAO;AACd,gBAAM,SAAS,KAAK,eAAe,KAAK;AACxC,cAAI,QAAQ;AACV,iBAAK,QAAQ,qBAAqB,IAAI,MAAM,CAAC;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO,WAAS;AACd,YAAI,KAAK,OAAO;AACd,gBAAM,SAAS,KAAK,eAAe,KAAK;AACxC,cAAI,QAAQ;AACV,iBAAK,QAAQ,sBAAsB,IAAI,MAAM,CAAC;AAAA,UAChD;AAAA,QACF;AACA,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,QAAQ,WAAS;AACf,YAAI,KAAK,OAAO;AACd,gBAAM,SAAS,KAAK,eAAe,KAAK;AACxC,cAAI,QAAQ;AACV,iBAAK,QAAQ,uBAAuB,IAAI,MAAM,CAAC;AAAA,UACjD;AAAA,QACF;AACA,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,eAAkB,SAAqC;AACrD,QAAI;AACF,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B,SAAS,KAAK;AACZ,WAAK,QAAQ,GAAG;AAChB,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAS,SAAkB;AACzB,UAAM,SAAS,KAAK,eAAe,OAAO;AAC1C,QAAI,QAAQ;AACV,WAAK,gBAAgB,QAAQ,IAAI;AACjC,WAAK,UAAU;AACf,WAAK,MAAM;AAGX,UAAI,KAAK,WAAW,GAAG;AACrB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAgB;AACd,QAAI,KAAK,OAAO;AACd,WAAK,OAAO;AACZ,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EAEA,UAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AACF;;;AC5TA,IAAqB,2BAArB,cAAsD,wBAAwB;AAAA,EAA9E;AAAA;AACE,SAAS,OAAoB;AAAA;AAC/B;;;ACFA,IAAqB,yBAArB,cAAoD,sBAAsB;AAAA,EAA1E;AAAA;AACE,SAAS,OAAoB;AAAA;AAC/B;;;ACSO,SAAS,eACd,QACA,UAAiC,CAAC,GAC1B;AACR,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,uBAAuB;AAAA,IACrC;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B,MAAM,QAAQ;AAAA,EAChB,CAAC;AACD,QAAM,OAAO,IAAI,SAAS,MAAM;AAChC,QAAM,SAAS,IAAI,uBAAuB;AAAA,IACxC;AAAA,IACA,UAAU,IAAI;AAAA,IACd,SAAS,QAAQ;AAAA,IACjB,YAAY,IAAI;AAAA,EAClB,CAAC;AACD,SAAO,OAAO,aAAa,IAAI;AACjC;AAMA,eAAsB,oBACpB,QACA,UAAsC,CAAC,GACtB;AACjB,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,wBAAwB;AAAA,IACtC;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B,MAAM,QAAQ;AAAA,EAChB,CAAC;AACD,QAAM,OAAO,MAAM,IAAI,SAAS,MAAM;AACtC,QAAM,SAAS,IAAI,uBAAuB;AAAA,IACxC;AAAA,IACA,UAAU,IAAI;AAAA,IACd,SAAS,QAAQ;AAAA,IACjB,YAAY,IAAI;AAAA,EAClB,CAAC;AACD,SAAO,OAAO,aAAa,IAAI;AACjC;AAIO,SAAS,YACd,QACA,UAAqC,CAAC,GACzB;AACb,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,uBAAuB;AAAA,IACrC;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B,MAAM,QAAQ;AAAA,EAChB,CAAC;AACD,SAAO,IAAI,SAAS,MAAM;AAC5B;AAIA,eAAsB,iBACpB,QACA,UAAqC,CAAC,GAChB;AACtB,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,wBAAwB;AAAA,IACtC;AAAA,IACA,kBAAkB,QAAQ;AAAA,IAC1B,MAAM,QAAQ;AAAA,EAChB,CAAC;AACD,SAAO,MAAM,IAAI,SAAS,MAAM;AAClC;AAQO,SAAS,qBACd,QACA,SACY;AACZ,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,yBAAyB;AAAA,IACvC;AAAA,IACA,MAAM,QAAQ;AAAA,IACd,kBAAkB,QAAQ;AAAA,IAC1B,QAAQ,MAAM,SAAe;AAC3B,YAAM,SAAS,IAAI,uBAAuB;AAAA,QACxC;AAAA,QACA,UAAU,IAAI;AAAA,QACd,SAAS,QAAQ;AAAA,QACjB,YAAY,IAAI;AAAA,MAClB,CAAC;AAED,UAAI;AAEJ,UAAI;AACF,qBAAa,OAAO,aAAa,IAAI;AAAA,MACvC,SAAS,KAAK;AACZ,YAAI,QAAQ,SAAS;AACnB,kBAAQ,QAAQ,GAAG;AAAA,QACrB;AACA;AAAA,MACF;AAEA,cAAQ,YAAY,YAAY,OAAO;AAAA,IACzC;AAAA,IACA,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,MAAI,MAAM,MAAM;AAEhB,SAAO,IAAI,QAAQ,KAAK,GAAG;AAC7B;AAIO,SAAS,kBACd,QACA,SACY;AACZ,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,yBAAyB;AAAA,IACvC;AAAA,IACA,MAAM,QAAQ;AAAA,IACd,kBAAkB,QAAQ;AAAA,IAC1B,SAAS,QAAQ;AAAA,IACjB,SAAS,QAAQ;AAAA,IACjB,QAAQ,QAAQ;AAAA,EAClB,CAAC;AAED,MAAI,MAAM,MAAM;AAEhB,SAAO,IAAI,QAAQ,KAAK,GAAG;AAC7B;AAIO,SAAS,cACd,QACA,SACG;AACH,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,yBAAyB;AAAA,IACvC;AAAA,IACA,MAAM,QAAQ;AAAA,EAChB,CAAC;AACD,SAAO,IAAI,eAAe,MAAM;AAClC;;;ACnKA,IAAqB,qBAArB,cAAgD,uBAAuB;AAAA,EAAvE;AAAA;AACE,SAAS,OAAoB;AAAA;AAC/B;;;ACCA,IAAqB,6BAArB,cAAwD,wBAAwB;AAAA,EAK9E,YAAY,SAA4C;AACtD,UAAM,OAAO;AALf,SAAS,OAAoB;AAM3B,SAAK,SAAS,IAAI,IAAI,QAAQ,UAAU;AAAA,EAC1C;AAAA,EAEA,mBAAsB,OAAe,OAAa;AAChD,QAAI,KAAK,OAAO,IAAI,KAAK,GAAG;AAC1B,WAAK,KAAK,IAAI,OAAO,KAAK;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AACF;;;ACxBA,IAAM,kBAAkC;AACxC,IAAM,sBAAsC,gBAAgB;AAC5D,IAAM,YACY;AAClB,IAAM,gBAAgC,UAAU;AAEjC,SAAR,cAA+B,OAAuB;AAC3D,MAAI,MAAM,QAAQ;AAClB,MAAI,MAAM,gBAAgB,GAAG;AAC7B,WAAS,QAAQ,OAAO;AACxB,SAAO,QAAQ,GAAG;AAChB,UAAM,QAAQ;AACd,WAAO,UAAU,GAAG;AACpB,aAAS,QAAQ,OAAO;AAAA,EAC1B;AACA,SAAO;AACT;;;ACFA,IAAqB,2BAArB,cAAsD,sBAAsB;AAAA,EAA5E;AAAA;AACE,SAAS,OAAoB;AAM7B;AAAA;AAAA;AAAA;AAAA,iBAAQ,oBAAI,IAAoB;AAMhC;AAAA;AAAA;AAAA;AAAA,gBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,YAAY,OAAuB;AAOjC,QAAI,cAAc,KAAK,MAAM,IAAI,KAAK;AACtC,QAAI,eAAe,MAAM;AACvB,oBAAc,KAAK,MAAM;AACzB,WAAK,MAAM,IAAI,OAAO,WAAW;AAAA,IACnC;AACA,QAAI,aAAa,KAAK,KAAK,WAAW;AACtC,QAAI,cAAc,MAAM;AACtB,mBAAa,cAAc,WAAW;AACtC,WAAK,KAAK,WAAW,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EAEU,mBAAmB,OAAe,OAAuB;AACjE,QAAI,KAAK,SAAS,KAAK,GAAG;AACxB,aAAO,KAAK,YAAY,KAAK,IAAI,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EAEU,4BACR,MACQ;AACR,UAAM,IAAI,4BAA4B,IAAI;AAAA,EAC5C;AAAA,EAEU,wBAAwB,MAAyC;AACzE,UAAM,IAAI,4BAA4B,IAAI;AAAA,EAC5C;AAAA,EAEU,uBAAuB,MAAwC;AACvE,UAAM,IAAI,4BAA4B,IAAI;AAAA,EAC5C;AAAA,EAEA,aAAa,MAA2B;AACtC,UAAM,SAAS,KAAK,UAAU,IAAI;AAElC,QAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACtC,YAAM,UAAU,KAAK,eAAe;AACpC,UAAI,OAAO;AACX,UAAI,SAAS;AAEX,cAAM,QAAQ,KAAK,YAAY,KAAK,CAAC;AACrC,eAAO,SAAS,MAAM,UAAU;AAChC,YAAI,CAAC,OAAO,WAAW,QAAQ,GAAG,GAAG;AACnC,iBAAO,QAAQ,MAAM;AAAA,QACvB;AACA,eAAO,MAAM,OAAO;AAAA,MACtB;AACA,aAAO,MAAM,KAAK,eAAe,KAAK,MAAM,IAAI,IAAI;AAAA,IACtD;AACA,QAAI,KAAK,uBAA8B;AACrC,aAAO,MAAM,SAAS;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACF;;;AC3FA,IAAqB,oBAArB,cAA+C,sBAAsB;AAAA,EAArE;AAAA;AACE,SAAS,OAAoB;AAAA;AAC/B;;;ACCO,SAAS,UACd,QACA,UAAoC,CAAC,GAC7B;AACR,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,kBAAkB;AAAA,IAChC;AAAA,IACA,kBAAkB,QAAQ;AAAA,EAC5B,CAAC;AACD,QAAM,OAAO,IAAI,SAAS,MAAM;AAChC,QAAM,SAAS,IAAI,yBAAyB;AAAA,IAC1C;AAAA,IACA,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB,CAAC;AACD,SAAO,OAAO,aAAa,IAAI;AACjC;AAEA,eAAsB,eACpB,QACA,UAAqC,CAAC,GACrB;AACjB,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,mBAAmB;AAAA,IACjC;AAAA,IACA,kBAAkB,QAAQ;AAAA,EAC5B,CAAC;AACD,QAAM,OAAO,MAAM,IAAI,SAAS,MAAM;AACtC,QAAM,SAAS,IAAI,yBAAyB;AAAA,IAC1C;AAAA,IACA,UAAU,IAAI;AAAA,IACd,YAAY,IAAI;AAAA,EAClB,CAAC;AACD,SAAO,OAAO,aAAa,IAAI;AACjC;AAEO,SAAS,YAAe,QAAmB;AAChD,UAAQ,GAAG,MAAM,MAAM;AACzB;AAQO,SAAS,OACd,QACA,UAAoC,CAAC,GACxB;AACb,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,kBAAkB;AAAA,IAChC;AAAA,IACA,kBAAkB,QAAQ;AAAA,EAC5B,CAAC;AACD,SAAO;AAAA,IACL,GAAG,IAAI,SAAS,MAAM;AAAA,IACtB,GAAG,IAAI;AAAA,IACP,GAAG,MAAM,KAAK,IAAI,MAAM;AAAA,EAC1B;AACF;AAEA,eAAsB,YACpB,QACA,UAAqC,CAAC,GAChB;AACtB,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,mBAAmB;AAAA,IACjC;AAAA,IACA,kBAAkB,QAAQ;AAAA,EAC5B,CAAC;AACD,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,SAAS,MAAM;AAAA,IAC5B,GAAG,IAAI;AAAA,IACP,GAAG,MAAM,KAAK,IAAI,MAAM;AAAA,EAC1B;AACF;AAEO,SAAS,YACd,QACA,UAA+B,CAAC,GACxB;AACR,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,yBAAyB;AAAA,IACvC;AAAA,IACA,UAAU,OAAO;AAAA,IACjB,YAAY,OAAO;AAAA,EACrB,CAAC;AACD,SAAO,IAAI,aAAa,OAAO,CAAC;AAClC;AAEO,SAAS,SACd,QACA,UAA+B,CAAC,GAC7B;AACH,QAAM,UAAU,eAAe,QAAQ,OAAO;AAC9C,QAAM,MAAM,IAAI,2BAA2B;AAAA,IACzC;AAAA,IACA,YAAY,OAAO;AAAA,EACrB,CAAC;AACD,SAAO,IAAI,eAAe,OAAO,CAAC;AACpC;;;AC7FA,IAAqB,aAArB,MAAgC;AAAA,EAe9B,YAAoB,SAA4B;AAA5B;AAdpB,SAAQ,QAAQ;AAEhB,SAAQ,UAAU;AAElB,SAAQ,OAAO;AAEf,SAAQ,UAAU;AAElB,SAAQ,WAA2B,CAAC;AAEpC,SAAQ,OAAO,oBAAI,IAAqB;AAQxC,gBAAO,oBAAI,IAAY;AA6CvB,eAAM;AAhDJ,SAAK,UAAU,eAAe,QAAQ,OAAO;AAAA,EAC/C;AAAA,EAIA,MAAM,KAAa,OAAsB;AACvC,QAAI,KAAK,SAAS,CAAC,KAAK,SAAS;AAC/B,WAAK;AACL,WAAK,KAAK,IAAI,GAAG;AACjB,WAAK,SAAS;AAAA,QACZ,qBAAqB,OAAO;AAAA,UAC1B,SAAS,KAAK;AAAA,UACd,SAAS,KAAK,QAAQ;AAAA,UACtB,MAAM,KAAK;AAAA,UACX,kBAAkB,KAAK,QAAQ;AAAA,UAC/B,SAAS,KAAK,QAAQ;AAAA,UACtB,aAAa,CAAC,MAAM,YAAY;AAC9B,gBAAI,KAAK,OAAO;AACd,mBAAK,QAAQ;AAAA,gBACX,UACI,KAAK,QAAQ,mBACX,OACA,gBAAgB,GAAG,IACnB,QACA,OACF;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ,MAAM;AACZ,gBAAI,KAAK,OAAO;AACd,mBAAK;AACL,kBACE,KAAK,WAAW,KAChB,KAAK,WACL,CAAC,KAAK,QACN,KAAK,QAAQ,QACb;AACA,qBAAK,QAAQ,OAAO;AACpB,qBAAK,OAAO;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EAIQ,YAAoB;AAC1B,WAAO,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,GAAG;AACnC,WAAK;AAAA,IACP;AACA,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEA,KAAK,OAAwB;AAC3B,UAAM,QAAQ,KAAK,UAAU;AAC7B,SAAK,MAAM,OAAO,KAAK;AACvB,WAAO;AAAA,EACT;AAAA,EAEA,QAAc;AACZ,QAAI,KAAK,OAAO;AACd,WAAK,UAAU;AACf,UAAI,KAAK,WAAW,KAAK,CAAC,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AAC1D,aAAK,QAAQ,OAAO;AACpB,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EAEA,QAAc;AACZ,QAAI,KAAK,OAAO;AACd,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,aAAK,SAAS,CAAC,EAAE;AAAA,MACnB;AACA,UAAI,CAAC,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AACrC,aAAK,QAAQ,OAAO;AACpB,aAAK,OAAO;AAAA,MACd;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;", "names": ["Feature"]}