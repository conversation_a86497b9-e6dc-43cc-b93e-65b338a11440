{"name": "@solid-primitives/resize-observer", "version": "2.1.1", "description": "Reactive primitives for observing resizing of HTML elements.", "author": "<PERSON><PERSON>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://primitives.solidjs.community/package/resize-observer", "repository": {"type": "git", "url": "git+https://github.com/solidjs-community/solid-primitives.git"}, "primitive": {"name": "resize-observer", "stage": 3, "list": ["createResizeObserver", "createWindowSize", "createElementSize"], "category": "Display & Media"}, "files": ["dist"], "private": false, "sideEffects": false, "type": "module", "module": "./dist/index.js", "types": "./dist/index.d.ts", "browser": {}, "exports": {"import": {"@solid-primitives/source": "./src/index.ts", "types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "keywords": ["resize", "observer", "solid", "primitives"], "dependencies": {"@solid-primitives/event-listener": "^2.4.1", "@solid-primitives/rootless": "^1.5.1", "@solid-primitives/static-store": "^0.1.1", "@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}, "typesVersions": {}, "devDependencies": {"solid-js": "^1.8.7"}, "scripts": {"dev": "tsx ../../scripts/dev.ts", "build": "tsx ../../scripts/build.ts", "vitest": "vitest -c ../../configs/vitest.config.ts", "test": "pnpm run vitest", "test:ssr": "pnpm run vitest --mode ssr"}}