{"name": "@solid-primitives/static-store", "version": "0.1.1", "description": "Primitives for creating small reactive objects that doesn't change their shape over time - don't need a proxy wrapper.", "author": "<PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "homepage": "https://primitives.solidjs.community/package/static-store", "repository": {"type": "git", "url": "git+https://github.com/solidjs-community/solid-primitives.git"}, "bugs": {"url": "https://github.com/solidjs-community/solid-primitives/issues"}, "primitive": {"name": "static-store", "stage": 2, "list": ["createStaticStore", "createDerivedStaticStore"], "category": "Reactivity"}, "keywords": ["solid", "primitives"], "private": false, "sideEffects": false, "files": ["dist"], "type": "module", "module": "./dist/index.js", "types": "./dist/index.d.ts", "browser": {}, "exports": {"import": {"@solid-primitives/source": "./src/index.ts", "types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "typesVersions": {}, "peerDependencies": {"solid-js": "^1.6.12"}, "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "devDependencies": {"solid-js": "^1.8.7"}, "scripts": {"dev": "tsx ../../scripts/dev.ts", "build": "tsx ../../scripts/build.ts", "vitest": "vitest -c ../../configs/vitest.config.ts", "test": "pnpm run vitest", "test:ssr": "pnpm run vitest --mode ssr"}}