import { LayerConfig } from "konva/lib/Layer";
import { ShapeConfig } from "konva/lib/Shape";
import { Stage as KStage, StageConfig } from "konva/lib/Stage";
import type { JSX } from "solid-js/jsx-runtime";
import { KonvaEvents, TransformerEvents } from "./types";
declare function createStage(props: Omit<StageConfig, "container">): {
    ref: import("solid-js").Setter<HTMLDivElement>;
    containerRef: import("solid-js").Accessor<HTMLDivElement>;
    stage: import("solid-js").Accessor<KStage>;
};
export declare function StageContextProvider(props: {
    children: JSX.Element;
    stageProps: ReturnType<typeof createStage>;
}): JSX.Element;
export declare function useStage(): {
    ref: import("solid-js").Setter<HTMLDivElement>;
    containerRef: import("solid-js").Accessor<HTMLDivElement>;
    stage: import("solid-js").Accessor<KStage>;
};
export declare function Stage(props: JSX.HTMLAttributes<HTMLDivElement> & Omit<StageConfig, "container">): JSX.Element;
export declare function Layer(props: {
    children?: JSX.Element;
} & LayerConfig): JSX.Element;
export declare const Group: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Rect: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Circle: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Ellipse: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Wedge: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Line: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Sprite: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Image: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Text: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const TextPath: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Star: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Ring: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Arc: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Tag: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Path: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const RegularPolygon: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Arrow: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Shape: (props: ShapeConfig & KonvaEvents) => JSX.Element;
export declare const Transformer: (props: ShapeConfig & KonvaEvents & TransformerEvents) => JSX.Element;
export {};
