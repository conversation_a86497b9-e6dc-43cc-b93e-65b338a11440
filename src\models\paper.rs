use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Paper {
    pub id: Uuid,
    pub title: String,
    pub authors: Vec<String>,
    pub journal: Option<String>,
    pub year: Option<u32>,
    pub doi: Option<String>,
    pub abstract_text: Option<String>,
    pub keywords: Vec<String>,
    #[serde(default)]
    pub files: Vec<PaperFile>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct CreatePaperRequest {
    pub title: String,
    pub authors: Vec<String>,
    pub journal: Option<String>,
    pub year: Option<u32>,
    pub doi: Option<String>,
    pub abstract_text: Option<String>,
    pub keywords: Vec<String>,
    #[serde(default)]
    pub folder_path: Option<String>, // 可选的子文件夹路径，None表示根目录，用于指定创建位置
}

#[derive(Debug, Deserialize)]
pub struct UpdatePaperRequest {
    pub title: Option<String>,
    pub authors: Option<Vec<String>>,
    pub journal: Option<String>,
    pub year: Option<u32>,
    pub doi: Option<String>,
    pub abstract_text: Option<String>,
    pub keywords: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct RenameFileRequest {
    pub new_name: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaperFile {
    pub name: String,
    pub file_type: FileType,
    pub relative_path: String, // 相对于paper目录的路径，如 "origin.pdf", "images/pic1.jpg", "notes/note1.txt"
    pub size: u64,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileType {
    Image,
    Note,
    Origin, // origin.pdf
}



#[derive(Debug, Serialize)]
pub struct PaginatedPapers {
    pub papers: Vec<Paper>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub page_size: u32,
    pub total: u32,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}



impl CreatePaperRequest {
    /// 获取相对文件夹路径（用于显示和索引）
    pub fn get_relative_folder_path(&self) -> String {
        self.folder_path
            .as_ref()
            .map(|s| s.trim())
            .filter(|s| !s.is_empty())
            .unwrap_or("") // 空字符串表示根目录
            .to_string()
    }

    /// 获取完整的存储路径
    pub fn get_storage_path(&self) -> String {
        let config = crate::config::get_config();
        let base_dir = &config.storage.papers_directory;

        let relative_path = self.get_relative_folder_path();
        if relative_path.is_empty() {
            base_dir.clone() // 根目录
        } else {
            format!("{}/{}", base_dir, relative_path)
        }
    }
}

impl PaginationInfo {
    pub fn new(page: u32, page_size: u32, total: u32) -> Self {
        let total_pages = if total == 0 { 1 } else { (total + page_size - 1) / page_size };
        let has_next = page < total_pages;
        let has_prev = page > 1;

        Self {
            page,
            page_size,
            total,
            total_pages,
            has_next,
            has_prev,
        }
    }
}

impl Paper {
    pub fn new(request: CreatePaperRequest) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            title: request.title,
            authors: request.authors,
            journal: request.journal,
            year: request.year,
            doi: request.doi,
            abstract_text: request.abstract_text,
            keywords: request.keywords,
            files: Vec::new(), // 初始化为空，文件上传时会添加
            created_at: now,
            updated_at: now,
        }
    }

    /// 获取文件夹路径（从文件系统路径推导）
    /// 需要传入paper的物理路径来推导虚拟文件夹路径
    pub fn get_folder_path(&self, paper_physical_path: &std::path::Path) -> String {
        let config = crate::config::get_config();
        let papers_dir = std::path::PathBuf::from(&config.storage.papers_directory);

        // 获取paper目录的父目录
        if let Some(parent_dir) = paper_physical_path.parent() {
            // 计算相对于papers_directory的路径
            if let Ok(relative_path) = parent_dir.strip_prefix(&papers_dir) {
                // 转换为字符串，空路径表示根目录
                relative_path.to_string_lossy().to_string()
            } else {
                // 如果无法计算相对路径，返回空字符串（根目录）
                String::new()
            }
        } else {
            // 如果无法获取父目录，返回空字符串（根目录）
            String::new()
        }
    }

    /// 获取文件夹路径的简化版本（用于缓存等不需要物理路径的场景）
    /// 这个方法将被逐步替换
    pub fn get_folder_path_cached(&self) -> String {
        // 这个方法现在无法从Paper对象本身推导路径
        // 需要调用方提供路径信息，或者从缓存中获取
        // 暂时返回空字符串，表示根目录
        String::new()
    }

    /// 获取显示用的文件夹名称
    pub fn get_folder_display_name(&self, paper_physical_path: &std::path::Path) -> String {
        let path = self.get_folder_path(paper_physical_path);
        if path.is_empty() {
            let config = crate::config::get_config();
            format!("根目录 ({})", config.storage.papers_directory)
        } else {
            path
        }
    }

    /// 获取完整的存储路径
    pub fn get_storage_path(&self, paper_physical_path: &std::path::Path) -> String {
        let config = crate::config::get_config();
        let base_dir = &config.storage.papers_directory;
        let folder_path = self.get_folder_path(paper_physical_path);

        if folder_path.is_empty() {
            base_dir.clone()
        } else {
            format!("{}/{}", base_dir, folder_path)
        }
    }

    pub fn update(&mut self, request: UpdatePaperRequest) {
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(authors) = request.authors {
            self.authors = authors;
        }
        if let Some(journal) = request.journal {
            self.journal = Some(journal);
        }
        if let Some(year) = request.year {
            self.year = Some(year);
        }
        if let Some(doi) = request.doi {
            self.doi = Some(doi);
        }
        if let Some(abstract_text) = request.abstract_text {
            self.abstract_text = Some(abstract_text);
        }
        if let Some(keywords) = request.keywords {
            self.keywords = keywords;
        }
        self.updated_at = Utc::now();
    }

    /// 添加文件到Paper
    pub fn add_file(&mut self, file: PaperFile) {
        // 如果文件已存在，先移除旧的
        self.files.retain(|f| f.name != file.name || f.file_type != file.file_type);
        self.files.push(file);
        self.updated_at = Utc::now();
    }

    /// 移除文件
    pub fn remove_file(&mut self, filename: &str) -> bool {
        let original_len = self.files.len();
        self.files.retain(|f| f.name != filename);
        let removed = self.files.len() != original_len;
        if removed {
            self.updated_at = Utc::now();
        }
        removed
    }

    /// 获取指定文件
    pub fn get_file(&self, filename: &str) -> Option<&PaperFile> {
        self.files.iter().find(|f| f.name == filename)
    }

    /// 获取指定类型的文件列表
    pub fn get_files_by_type(&self, file_type: &FileType) -> Vec<&PaperFile> {
        self.files.iter().filter(|f| &f.file_type == file_type).collect()
    }

    /// 重命名文件（不允许重命名origin.pdf）
    pub fn rename_file(&mut self, old_name: &str, new_name: &str) -> Result<(), String> {
        // 查找要重命名的文件
        let file_index = self.files.iter().position(|f| f.name == old_name)
            .ok_or_else(|| "File not found".to_string())?;

        let file = &self.files[file_index];

        // 不允许重命名origin.pdf
        if file.file_type == FileType::Origin {
            return Err("Cannot rename origin file".to_string());
        }

        // 检查新文件名是否已存在
        if self.files.iter().any(|f| f.name == new_name && f.file_type == file.file_type) {
            return Err("File with new name already exists".to_string());
        }

        // 更新文件信息
        let mut updated_file = file.clone();
        updated_file.name = new_name.to_string();

        // 更新relative_path
        let path_parts: Vec<&str> = updated_file.relative_path.rsplitn(2, '/').collect();
        if path_parts.len() == 2 {
            // 有路径前缀，如 "images/old_name.jpg" -> "images/new_name.jpg"
            updated_file.relative_path = format!("{}/{}", path_parts[1], new_name);
        } else {
            // 没有路径前缀，如 "old_name.txt" -> "new_name.txt"
            updated_file.relative_path = new_name.to_string();
        }

        // 替换文件记录
        self.files[file_index] = updated_file;
        self.updated_at = Utc::now();

        Ok(())
    }
}
