(function(u,l){typeof exports=="object"&&typeof module<"u"?l(exports,require("solid-js"),require("konva")):typeof define=="function"&&define.amd?define(["exports","solid-js","konva"],l):(u=typeof globalThis<"u"?globalThis:u||self,l(u["solid-konva"]={},u.Solid$$,u.Konva))})(this,function(u,l,I){"use strict";const S=(t=>t&&typeof t=="object"&&"default"in t?t:{default:t})(I),K=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected"],q=new Set(["className","value","readOnly","formNoValidate","isMap","noModule","playsInline",...K]),W=new Set(["innerHTML","textContent","innerText","children"]),H={className:"class",htmlFor:"for"},A={class:"className",formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly"},V=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]),U={xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace"};function X(t,n,e){let o=e.length,f=n.length,c=o,s=0,i=0,a=n[f-1].nextSibling,r=null;for(;s<f||i<c;){if(n[s]===e[i]){s++,i++;continue}for(;n[f-1]===e[c-1];)f--,c--;if(f===s){const d=c<o?i?e[i-1].nextSibling:e[c-i]:a;for(;i<c;)t.insertBefore(e[i++],d)}else if(c===i)for(;s<f;)(!r||!r.has(n[s]))&&n[s].remove(),s++;else if(n[s]===e[c-1]&&e[i]===n[f-1]){const d=n[--f].nextSibling;t.insertBefore(e[i++],n[s++].nextSibling),t.insertBefore(e[--c],d),n[f]=e[c]}else{if(!r){r=new Map;let g=i;for(;g<c;)r.set(e[g],g++)}const d=r.get(n[s]);if(d!=null)if(i<d&&d<c){let g=s,y=1,m;for(;++g<f&&g<c&&!((m=r.get(n[g]))==null||m!==d+y);)y++;if(y>d-i){const b=n[s];for(;i<d;)t.insertBefore(e[i++],b)}else t.replaceChild(e[i++],n[s++])}else s++;else n[s++].remove()}}}const L="_$DX_DELEGATE";function G(t,n,e){const o=document.createElement("template");o.innerHTML=t;let f=o.content.firstChild;return e&&(f=f.firstChild),f}function F(t,n=window.document){const e=n[L]||(n[L]=new Set);for(let o=0,f=t.length;o<f;o++){const c=t[o];e.has(c)||(e.add(c),n.addEventListener(c,ie))}}function Y(t,n,e){e==null?t.removeAttribute(n):t.setAttribute(n,e)}function Q(t,n,e,o){o==null?t.removeAttributeNS(n,e):t.setAttributeNS(n,e,o)}function Z(t,n){n==null?t.removeAttribute("class"):t.className=n}function v(t,n,e,o){if(o)Array.isArray(e)?(t[`$$${n}`]=e[0],t[`$$${n}Data`]=e[1]):t[`$$${n}`]=e;else if(Array.isArray(e)){const f=e[0];t.addEventListener(n,e[0]=c=>f.call(t,e[1],c))}else t.addEventListener(n,e)}function p(t,n,e={}){const o=Object.keys(n||{}),f=Object.keys(e);let c,s;for(c=0,s=f.length;c<s;c++){const i=f[c];!i||i==="undefined"||n[i]||(P(t,i,!1),delete e[i])}for(c=0,s=o.length;c<s;c++){const i=o[c],a=!!n[i];!i||i==="undefined"||e[i]===a||!a||(P(t,i,!0),e[i]=a)}return e}function J(t,n,e={}){const o=t.style,f=typeof e=="string";if(n==null&&f||typeof n=="string")return o.cssText=n;f&&(o.cssText=void 0,e={}),n||(n={});let c,s;for(s in e)n[s]==null&&o.removeProperty(s),delete e[s];for(s in n)c=n[s],c!==e[s]&&(o.setProperty(s,c),e[s]=c);return e}function ee(t,n,e,o){typeof n=="function"?l.createRenderEffect(f=>R(t,n(),f,e,o)):R(t,n,void 0,e,o)}function T(t,n,e,o){if(e!==void 0&&!o&&(o=[]),typeof n!="function")return w(t,n,o,e);l.createRenderEffect(f=>w(t,n(),f,e),o)}function te(t,n,e,o,f={},c=!1){n||(n={});for(const s in f)if(!(s in n)){if(s==="children")continue;N(t,s,null,f[s],e,c)}for(const s in n){if(s==="children"){o||w(t,n.children);continue}const i=n[s];f[s]=N(t,s,i,f[s],e,c)}}function ne(t){return t.toLowerCase().replace(/-([a-z])/g,(n,e)=>e.toUpperCase())}function P(t,n,e){const o=n.trim().split(/\s+/);for(let f=0,c=o.length;f<c;f++)t.classList.toggle(o[f],e)}function N(t,n,e,o,f,c){let s,i,a;if(n==="style")return J(t,e,o);if(n==="classList")return p(t,e,o);if(e===o)return o;if(n==="ref")c||e(t);else if(n.slice(0,3)==="on:"){const r=n.slice(3);o&&t.removeEventListener(r,o),e&&t.addEventListener(r,e)}else if(n.slice(0,10)==="oncapture:"){const r=n.slice(10);o&&t.removeEventListener(r,o,!0),e&&t.addEventListener(r,e,!0)}else if(n.slice(0,2)==="on"){const r=n.slice(2).toLowerCase(),d=V.has(r);if(!d&&o){const g=Array.isArray(o)?o[0]:o;t.removeEventListener(r,g)}(d||e)&&(v(t,r,e,d),d&&F([r]))}else if((a=W.has(n))||!f&&(A[n]||(i=q.has(n)))||(s=t.nodeName.includes("-")))n==="class"||n==="className"?Z(t,e):s&&!i&&!a?t[ne(n)]=e:t[A[n]||n]=e;else{const r=f&&n.indexOf(":")>-1&&U[n.split(":")[0]];r?Q(t,r,n,e):Y(t,H[n]||n,e)}return e}function ie(t){const n=`$$${t.type}`;let e=t.composedPath&&t.composedPath()[0]||t.target;for(t.target!==e&&Object.defineProperty(t,"target",{configurable:!0,value:e}),Object.defineProperty(t,"currentTarget",{configurable:!0,get(){return e||document}}),l.sharedConfig.registry&&!l.sharedConfig.done&&(l.sharedConfig.done=!0,document.querySelectorAll("[id^=pl-]").forEach(o=>o.remove()));e!==null;){const o=e[n];if(o&&!e.disabled){const f=e[`${n}Data`];if(f!==void 0?o.call(e,f,t):o.call(e,t),t.cancelBubble)return}e=e.host&&e.host!==e&&e.host instanceof Node?e.host:e.parentNode}}function R(t,n,e={},o,f){return n||(n={}),!f&&"children"in n&&l.createRenderEffect(()=>e.children=w(t,n.children,e.children)),n.ref&&n.ref(t),l.createRenderEffect(()=>te(t,n,o,!0,e,!0)),e}function w(t,n,e,o,f){for(l.sharedConfig.context&&!e&&(e=[...t.childNodes]);typeof e=="function";)e=e();if(n===e)return e;const c=typeof n,s=o!==void 0;if(t=s&&e[0]&&e[0].parentNode||t,c==="string"||c==="number"){if(l.sharedConfig.context)return e;if(c==="number"&&(n=n.toString()),s){let i=e[0];i&&i.nodeType===3?i.data=n:i=document.createTextNode(n),e=C(t,e,o,i)}else e!==""&&typeof e=="string"?e=t.firstChild.data=n:e=t.textContent=n}else if(n==null||c==="boolean"){if(l.sharedConfig.context)return e;e=C(t,e,o)}else{if(c==="function")return l.createRenderEffect(()=>{let i=n();for(;typeof i=="function";)i=i();e=w(t,i,e,o)}),()=>e;if(Array.isArray(n)){const i=[],a=e&&Array.isArray(e);if(E(i,n,e,f))return l.createRenderEffect(()=>e=w(t,i,e,o,!0)),()=>e;if(l.sharedConfig.context){for(let r=0;r<i.length;r++)if(i[r].parentNode)return e=i}if(i.length===0){if(e=C(t,e,o),s)return e}else a?e.length===0?$(t,i,o):X(t,e,i):(e&&C(t),$(t,i));e=i}else if(n instanceof Node){if(l.sharedConfig.context&&n.parentNode)return e=s?[n]:n;if(Array.isArray(e)){if(s)return e=C(t,e,o,n);C(t,e,null,n)}else e==null||e===""||!t.firstChild?t.appendChild(n):t.replaceChild(n,t.firstChild);e=n}}return e}function E(t,n,e,o){let f=!1;for(let c=0,s=n.length;c<s;c++){let i=n[c],a=e&&e[c];if(i instanceof Node)t.push(i);else if(!(i==null||i===!0||i===!1))if(Array.isArray(i))f=E(t,i,a)||f;else if(typeof i=="function")if(o){for(;typeof i=="function";)i=i();f=E(t,Array.isArray(i)?i:[i],a)||f}else t.push(i),f=!0;else{const r=String(i);a&&a.nodeType===3&&a.data===r?t.push(a):t.push(document.createTextNode(r))}}return f}function $(t,n,e){for(let o=0,f=n.length;o<f;o++)t.insertBefore(n[o],e)}function C(t,n,e,o){if(e===void 0)return t.textContent="";const f=o||document.createTextNode("");if(n.length){let c=!1;for(let s=n.length-1;s>=0;s--){const i=n[s];if(f!==i){const a=i.parentNode===t;!c&&!s?a?t.replaceChild(f,i):t.insertBefore(f,e):a&&i.remove()}else c=!0}}else t.insertBefore(f,e);return[f]}function oe(t){return t!==null&&(typeof t=="object"||typeof t=="function")}var fe=t=>typeof t=="function"&&!t.length?t():t,x=t=>Array.isArray(t)?t:[t];function M(t,...n){return typeof t=="function"?t(...n):t}var se=Object.entries,ce=Object.keys;function re(t){const n={...t},e={},o=new Map,f=i=>{const a=o.get(i);if(a)return a[0]();const r=l.createSignal(n[i],{name:typeof i=="string"?i:void 0});return o.set(i,r),delete n[i],r[0]()},c=(i,a)=>{const r=o.get(i);if(r)return r[1](a);i in n&&(n[i]=M(a,[n[i]]))};for(const i of ce(t))e[i]=void 0,Object.defineProperty(e,i,{get:f.bind(void 0,i)});return[e,(i,a)=>(oe(i)?l.untrack(()=>{l.batch(()=>{for(const[r,d]of se(M(i,e)))c(r,()=>d)})}):c(i,a),e)]}function le(t,n,e,o){const f=t.length,c=n.length;let s=0;if(!c){for(;s<f;s++)e(t[s]);return}if(!f){for(;s<c;s++)o(n[s]);return}for(;s<c&&n[s]===t[s];s++);let i,a;n=n.slice(s),t=t.slice(s);for(i of n)t.includes(i)||o(i);for(a of t)n.includes(a)||e(a)}function ae(t,n){const e=new ResizeObserver(t);return l.onCleanup(e.disconnect.bind(e)),{observe:o=>e.observe(o,n),unobserve:e.unobserve.bind(e)}}function ue(t,n,e){const o=new WeakMap,{observe:f,unobserve:c}=ae(s,e);function s(a){for(const r of a){const{contentRect:d,target:g}=r,y=Math.round(d.width),m=Math.round(d.height),b=o.get(g);(!b||b.width!==y||b.height!==m)&&(n(d,r.target,r),o.set(g,{width:y,height:m}))}}let i;if(typeof t=="function")i=()=>x(t()).slice();else if(Array.isArray(t)&&l.$PROXY in t)i=()=>(t[l.$TRACK],t.slice());else{x(t).forEach(f);return}l.createEffect(l.on(i,(a,r=[])=>le(a,r,f,c)))}function O(t){if(!t)return{width:null,height:null};const{width:n,height:e}=t.getBoundingClientRect();return{width:n,height:e}}function de(t){const[n,e]=re(O(fe(t)));return typeof t=="function"&&l.onMount(()=>e(O(t()))),ue(typeof t=="function"?()=>t()||[]:t,f=>e({width:f.width,height:f.height})),n}const _=G("<div></div>");function he(t){const[n,e]=l.createSignal(),o=de(n),[f,c]=l.createSignal();return l.onMount(()=>{c(new S.default.Stage({height:o.width,width:o.height,container:n(),...t}))}),l.createEffect(()=>{f()?.setAttrs({width:o.width,height:o.height})}),l.onCleanup(()=>{f()?.destroy()}),{...t,ref:e,containerRef:n,stage:f}}const j=l.createContext(null);function k(t){return l.createComponent(j.Provider,{get value(){return t.stageProps},get children(){return t.children}})}function B(){return l.useContext(j)}function ge(t){const n=he({...t});return(()=>{const e=_.cloneNode(!0),o=n.ref;return typeof o=="function"?o(e):n.ref=e,ee(e,t,!1,!0),T(e,l.createComponent(k,{stageProps:n,get children(){return t.children}})),e})()}const D=l.createContext();function ye(){return l.useContext(D)}function me(t){const n=new S.default.Layer(t),e=B();return l.createEffect(()=>{e?.stage()&&e.stage().add(n)}),l.createEffect(()=>{n.setAttrs(t)}),l.onCleanup(()=>{console.log("murp"),n.destroy()}),(()=>{const o=_.cloneNode(!0);return T(o,l.createComponent(D.Provider,{value:{layer:n},get children(){return t.children}})),o})()}const z={children:!0,ref:!0,key:!0,style:!0,forwardedRef:!0,unstable_applyCache:!0,unstable_applyDrawHitFromCache:!0};function h(t){function n(e){let o={};const[f,c]=l.createSignal(null),s=ye();return l.onMount(()=>{const i=new S.default[t](e);c(i),s?.layer?.add(i)}),l.createEffect(()=>{!f()||f().setAttrs(e)}),l.createEffect(()=>{if(!f())return;if(o)for(const r in o){if(z[r])continue;const d=r.slice(0,2)==="on",g=o[r]!==e[r];if(d&&g){let m=r.substring(2).toLowerCase();m.substring(0,7)==="content"&&(m="content"+m.substring(7,1).toUpperCase()+m.substring(8)),f().off(m,o[r])}!e.hasOwnProperty(r)&&f().setAttr(r,void 0)}const i={};for(const r in e){if(z[r])continue;const d=r.slice(0,2)==="on",g=o[r]!==e[r];if(d&&g){let y=r.substring(2).toLowerCase();y.substring(0,7)==="content"&&(y="content"+y.substring(7,1).toUpperCase()+y.substring(8)),e[r]&&(i[y]=e[r])}}for(var a in i)f()?.on(a,i[a]);o=e}),l.onCleanup(()=>{f().destroy(),console.log("destroyed")}),[]}return n}const we=h("Group"),Ce=h("Rect"),be=h("Circle"),Se=h("Ellipse"),Ee=h("Wedge"),Ae=h("Line"),Le=h("Sprite"),Te=h("Image"),Pe=h("Text"),Ne=h("TextPath"),Re=h("Star"),$e=h("Ring"),xe=h("Arc"),Me=h("Tag"),Oe=h("Path"),_e=h("RegularPolygon"),je=h("Arrow"),ke=h("Shape"),Be=h("Transformer");u.Arc=xe,u.Arrow=je,u.Circle=be,u.Ellipse=Se,u.Group=we,u.Image=Te,u.Layer=me,u.Line=Ae,u.Path=Oe,u.Rect=Ce,u.RegularPolygon=_e,u.Ring=$e,u.Shape=ke,u.Sprite=Le,u.Stage=ge,u.StageContextProvider=k,u.Star=Re,u.Tag=Me,u.Text=Pe,u.TextPath=Ne,u.Transformer=Be,u.Wedge=Ee,u.useStage=B,Object.defineProperties(u,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
