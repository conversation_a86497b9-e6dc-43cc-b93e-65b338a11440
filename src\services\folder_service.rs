use crate::models::folder::{Folder, CreateFolderRequest, FolderDeleteResult};
use crate::error::{AppError, AppR<PERSON>ult};
use crate::utils::file_utils::{get_non_uuid_subfolders, count_paper_folders};
use std::path::{Path, PathBuf};
use tokio::fs;

pub struct FolderService;

impl FolderService {
    pub async fn list_folders(base_path: Option<&str>) -> AppResult<Vec<Folder>> {
        let papers_dir = PathBuf::from("papers");
        let target_path = if let Some(path) = base_path {
            papers_dir.join(path)
        } else {
            papers_dir
        };

        let mut folders = Vec::new();
        let subfolder_paths = get_non_uuid_subfolders(&target_path).await?;

        for path in subfolder_paths {
            let name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_string();

            let papers_count = count_paper_folders(&path).await?;
            let subfolders = Self::get_subfolders(&path).await?;

            let folder = Folder {
                name,
                path: path.clone(),
                papers_count,
                subfolders,
            };
            folders.push(folder);
        }

        folders.sort_by(|a, b| a.name.cmp(&b.name));
        Ok(folders)
    }

    pub async fn create_folder(request: CreateFolderRequest) -> AppResult<Folder> {
        let papers_dir = PathBuf::from("papers");
        let folder_path = if let Some(parent) = &request.parent_path {
            papers_dir.join(parent).join(&request.name)
        } else {
            papers_dir.join(&request.name)
        };

        if folder_path.exists() {
            return Err(AppError::folder_already_exists(
                folder_path.to_string_lossy().to_string()
            ));
        }

        fs::create_dir_all(&folder_path).await?;

        // 通知缓存更新（文件夹树会在下次访问时重新扫描）
        let cache = crate::get_cache();
        cache.touch().await;

        Ok(Folder::new(request.name, folder_path))
    }

    /// 安全删除文件夹（只删除空文件夹）
    pub async fn delete_folder(folder_path: &str) -> AppResult<()> {
        let papers_dir = PathBuf::from("papers");
        let target_path = papers_dir.join(folder_path);

        if !target_path.exists() {
            return Err(AppError::folder_not_found(folder_path));
        }

        // 检查文件夹是否包含文献
        let cache = crate::get_cache();
        let (has_papers, papers_count) = cache.has_papers_in_folder(folder_path).await;

        if has_papers {
            return Err(AppError::folder_not_empty(folder_path.to_string(), papers_count));
        }

        // 只删除空文件夹
        fs::remove_dir_all(target_path).await?;

        // 更新缓存时间戳
        cache.touch().await;

        Ok(())
    }

    /// 强制删除文件夹（包括其中的所有文献）
    pub async fn delete_folder_force(folder_path: &str) -> AppResult<FolderDeleteResult> {
        let papers_dir = PathBuf::from("papers");
        let target_path = papers_dir.join(folder_path);

        if !target_path.exists() {
            return Err(AppError::folder_not_found(folder_path));
        }

        // 从缓存中删除该文件夹下的所有文献
        let cache = crate::get_cache();
        let removed_paper_ids = cache.remove_folder_and_papers(folder_path).await?;

        // 删除文件系统中的文件夹
        fs::remove_dir_all(target_path).await?;

        let result = FolderDeleteResult {
            folder_path: folder_path.to_string(),
            deleted_papers_count: removed_paper_ids.len(),
            deleted_paper_ids: removed_paper_ids,
            force_delete: true,
        };

        println!("强制删除文件夹 '{}' 及其 {} 篇文献", folder_path, result.deleted_papers_count);

        Ok(result)
    }

    pub async fn rename_folder(old_path: &str, new_name: &str) -> AppResult<Folder> {
        let papers_dir = PathBuf::from("papers");
        let old_full_path = papers_dir.join(old_path);

        if !old_full_path.exists() {
            return Err(AppError::folder_not_found(old_path));
        }

        let parent = old_full_path.parent().unwrap_or(&papers_dir);
        let new_full_path = parent.join(new_name);

        if new_full_path.exists() {
            return Err(AppError::folder_already_exists(
                new_full_path.to_string_lossy().to_string()
            ));
        }

        // 计算新的文件夹路径（相对于papers目录）
        let new_path = if let Ok(relative_path) = new_full_path.strip_prefix(&papers_dir) {
            relative_path.to_string_lossy().to_string()
        } else {
            new_name.to_string()
        };

        // 重命名文件系统中的文件夹
        fs::rename(&old_full_path, &new_full_path).await?;

        // 更新缓存中的文件夹路径（不再需要更新meta.json文件）
        let cache = crate::get_cache();
        cache.update_folder_path(old_path, &new_path).await?;

        Ok(Folder::new(new_name.to_string(), new_full_path))
    }



    async fn get_subfolders(folder_path: &Path) -> AppResult<Vec<Folder>> {
        let mut subfolders = Vec::new();
        let subfolder_paths = get_non_uuid_subfolders(folder_path).await?;

        for path in subfolder_paths {
            let name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_string();

            let papers_count = count_paper_folders(&path).await?;
            let folder = Folder {
                name,
                path: path.clone(),
                papers_count,
                subfolders: Vec::new(), // 只获取一级子文件夹
            };
            subfolders.push(folder);
        }

        subfolders.sort_by(|a, b| a.name.cmp(&b.name));
        Ok(subfolders)
    }
}
