{"name": "@solid-primitives/event-listener", "version": "2.4.1", "description": "SolidJS Primitives to manage creating event listeners.", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://primitives.solidjs.community/package/event-listener", "repository": {"type": "git", "url": "git+https://github.com/solidjs-community/solid-primitives.git"}, "primitive": {"name": "event-listener", "stage": 3, "list": ["createEventListener", "createEventSignal", "createEventListenerMap", "WindowEventListener", "DocumentEventListener"], "category": "Browser APIs"}, "private": false, "sideEffects": false, "files": ["dist"], "type": "module", "module": "./dist/index.js", "browser": {}, "types": "./dist/index.d.ts", "exports": {"import": {"@solid-primitives/source": "./src/index.ts", "types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "keywords": ["event", "listener", "solid", "primitives"], "dependencies": {"@solid-primitives/utils": "^6.3.1"}, "peerDependencies": {"solid-js": "^1.6.12"}, "typesVersions": {}, "devDependencies": {"solid-js": "^1.8.7"}, "scripts": {"dev": "tsx ../../scripts/dev.ts", "build": "tsx ../../scripts/build.ts", "vitest": "vitest -c ../../configs/vitest.config.ts", "test": "pnpm run vitest", "test:ssr": "pnpm run vitest --mode ssr"}}