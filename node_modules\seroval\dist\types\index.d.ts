export { Feature } from './core/compat';
export { createReference } from './core/reference';
export * from './core/cross';
export * from './core/tree';
export { getCrossReferenceHeader } from './core/keys';
export * from './core/plugin';
export { default as Serializer } from './core/Serializer';
export { createStream } from './core/stream';
export type { Stream } from './core/stream';
export * from './core/errors';
export type { SerovalNode } from './core/types';
export { OpaqueReference } from './core/opaque-reference';
//# sourceMappingURL=index.d.ts.map