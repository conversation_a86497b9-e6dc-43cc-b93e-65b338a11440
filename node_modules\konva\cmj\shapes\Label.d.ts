import { Shape, ShapeConfig } from '../Shape';
import { Group } from '../Group';
import { Context } from '../Context';
import { ContainerConfig } from '../Container';
import { GetSet } from '../types';
import { Text } from './Text';
export interface LabelConfig extends ContainerConfig {
}
export declare class Label extends Group {
    constructor(config?: LabelConfig);
    getText(): Text;
    getTag(): Tag;
    _addListeners(text: any): void;
    getWidth(): number;
    getHeight(): number;
    _sync(): void;
}
export interface TagConfig extends ShapeConfig {
    pointerDirection?: string;
    pointerWidth?: number;
    pointerHeight?: number;
    cornerRadius?: number | Array<number>;
}
export declare class Tag extends Shape<TagConfig> {
    _sceneFunc(context: Context): void;
    getSelfRect(): {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    pointerDirection: GetSet<'left' | 'top' | 'right' | 'bottom', this>;
    pointerWidth: GetSet<number, this>;
    pointerHeight: GetSet<number, this>;
    cornerRadius: GetSet<number, this>;
}
